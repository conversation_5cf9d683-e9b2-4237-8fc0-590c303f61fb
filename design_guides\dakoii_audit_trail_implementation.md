# Unified Application Audit Trail Implementation Guide

This document outlines the implementation of a comprehensive audit trail feature for the **entire PROMIS application system**. This unified audit trail will track user actions across all portals (Dakoii, Admin, and Project Monitoring), providing centralized visibility for security, accountability, and troubleshooting.

**Key Benefits of Unified Approach**:
- Single source of truth for all application activities
- Cross-portal activity correlation and analysis
- Simplified maintenance and reporting
- Better overall system visibility
- Reduced database complexity
- Centralized audit log management

This implementation uses a battle-tested pattern that captures **only CRUD activity** (create, read*, update, delete) in a single audit table across all portals, with portal identification for proper context.

*Note: We normally skip plain "reads" because they don't alter state, but you can easily add them if needed.*

## 1. Database Setup - Unified Audit Trail

### Option 1: Using CodeIgniter 4 Migration (Recommended)

Create the migration file:

```bash
php spark make:migration CreateUnifiedAuditLogsTable
```

Update the migration file:

```php
// database/migrations/YYYY-MM-DD-HHMMSS_CreateUnifiedAuditLogsTable.php
<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateUnifiedAuditLogsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id'          => ['type' => 'BIGINT', 'auto_increment' => true],
            'table_name'  => ['type' => 'VARCHAR', 'constraint' => 100, 'comment' => 'Name of the table that was modified'],
            'primary_key' => ['type' => 'VARCHAR', 'constraint' => 64, 'comment' => 'Primary key value of the affected record'],
            'action'      => ['type' => 'ENUM', 'constraint' => ['create','update','delete','login','logout','access'], 'comment' => 'Type of operation performed'],
            'old_data'    => ['type' => 'MEDIUMTEXT', 'null' => true, 'comment' => 'JSON of data before change (for updates/deletes)'],
            'new_data'    => ['type' => 'MEDIUMTEXT', 'null' => true, 'comment' => 'JSON of data after change (for creates/updates)'],

            // User identification fields (flexible for different portal users)
            'user_id'     => ['type' => 'BIGINT', 'null' => true, 'comment' => 'ID of the user who performed the action'],
            'username'    => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true, 'comment' => 'Username of the user'],
            'user_type'   => ['type' => 'VARCHAR', 'constraint' => 20, 'null' => true, 'comment' => 'Type of user (dakoii_user, admin_user, project_officer, etc.)'],

            // Portal and context information
            'portal'      => ['type' => 'ENUM', 'constraint' => ['dakoii','admin','monitor'], 'comment' => 'Portal where the action was performed'],
            'module'      => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true, 'comment' => 'Module/section within the portal'],

            // Technical details
            'ip_address'  => ['type' => 'VARCHAR', 'constraint' => 45, 'null' => true, 'comment' => 'IP address of the user'],
            'user_agent'  => ['type' => 'TEXT', 'null' => true, 'comment' => 'Browser/client user agent string'],
            'session_id'  => ['type' => 'VARCHAR', 'constraint' => 128, 'null' => true, 'comment' => 'Session ID'],
            'request_url' => ['type' => 'VARCHAR', 'constraint' => 255, 'null' => true, 'comment' => 'URL where the action was performed'],

            // Additional context
            'description' => ['type' => 'TEXT', 'null' => true, 'comment' => 'Human-readable description of the action'],
            'created_at'  => ['type' => 'DATETIME', 'default' => 'CURRENT_TIMESTAMP', 'comment' => 'When the audit log was created'],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('table_name');
        $this->forge->addKey('user_id');
        $this->forge->addKey('portal');
        $this->forge->addKey('action');
        $this->forge->addKey('created_at');
        $this->forge->addKey('user_type');
        $this->forge->addKey('module');

        $this->forge->createTable('audit_logs');
    }

    public function down()
    {
        $this->forge->dropTable('audit_logs');
    }
}
```

Run the migration:

```bash
php spark migrate
```

### Option 2: Direct SQL (Alternative)

```sql
CREATE TABLE audit_logs (
    id                BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    table_name        VARCHAR(100) NOT NULL COMMENT 'Name of the table that was modified',
    primary_key       VARCHAR(64) NOT NULL COMMENT 'Primary key value of the affected record',
    action            ENUM('create','update','delete','login','logout','access') NOT NULL COMMENT 'Type of operation performed',
    old_data          MEDIUMTEXT NULL COMMENT 'JSON of data before change (for updates/deletes)',
    new_data          MEDIUMTEXT NULL COMMENT 'JSON of data after change (for creates/updates)',

    -- User identification fields (flexible for different portal users)
    user_id           BIGINT UNSIGNED NULL COMMENT 'ID of the user who performed the action',
    username          VARCHAR(50) NULL COMMENT 'Username of the user',
    user_type         VARCHAR(20) NULL COMMENT 'Type of user (dakoii_user, admin_user, project_officer, etc.)',

    -- Portal and context information
    portal            ENUM('dakoii','admin','monitor') NOT NULL COMMENT 'Portal where the action was performed',
    module            VARCHAR(50) NULL COMMENT 'Module/section within the portal',

    -- Technical details
    ip_address        VARCHAR(45) NULL COMMENT 'IP address of the user',
    user_agent        TEXT NULL COMMENT 'Browser/client user agent string',
    session_id        VARCHAR(128) NULL COMMENT 'Session ID',
    request_url       VARCHAR(255) NULL COMMENT 'URL where the action was performed',

    -- Additional context
    description       TEXT NULL COMMENT 'Human-readable description of the action',
    created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When the audit log was created',

    /* Indexes for efficient querying */
    INDEX idx_table_name (table_name),
    INDEX idx_user_id (user_id),
    INDEX idx_portal (portal),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_user_type (user_type),
    INDEX idx_module (module),
    INDEX idx_portal_user (portal, user_id),
    INDEX idx_portal_action (portal, action)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci
  COMMENT = 'Unified audit trail for all PROMIS application portals';
```

## 2. Create the Unified Auditable Trait

Create a reusable trait for all portals at `app/Traits/Auditable.php`:

```php
<?php

namespace App\Traits;

use Config\Services;
use App\Models\AuditLogModel;

/**
 * Unified Auditable Trait
 *
 * This trait provides automatic audit logging for all portals in the PROMIS application.
 * It automatically detects the portal context and logs activities accordingly.
 */
trait Auditable
{
    protected bool $auditSkip = false;   // allows manual opt-out per call

    // Automatically attached in Model::$beforeInsert / $beforeUpdate / $beforeDelete
    protected function audit(string $action, array $data): array
    {
        if ($this->auditSkip) {
            return $data;
        }   // skip when needed

        $portalContext = $this->detectPortalContext();
        if (!$portalContext) {
            return $data; // No valid portal context found
        }

        $request   = Services::request();
        $session   = session();
        $pkField   = $this->primaryKey;
        $pk        = $data['id'] ?? $data['data'][$pkField] ?? null;

        // Get user information based on portal context
        $userInfo = $this->getUserInfo($portalContext, $session);

        $old = null;
        $new = null;

        if ($action === 'update') {
            $old = json_encode($this->asArray()
                    ->where($pkField, $pk)
                    ->first(), JSON_UNESCAPED_UNICODE);
            $new = json_encode($data['data'], JSON_UNESCAPED_UNICODE);
        } elseif ($action === 'create') {
            $new = json_encode($data['data'], JSON_UNESCAPED_UNICODE);
        } elseif ($action === 'delete') {
            $old = json_encode($this->asArray()->where($pkField, $pk)->first(), JSON_UNESCAPED_UNICODE);
        }

        (new AuditLogModel())->insert([
            'table_name'  => $this->table,
            'primary_key' => (string) $pk,
            'action'      => $action,
            'old_data'    => $old,
            'new_data'    => $new,
            'user_id'     => $userInfo['user_id'],
            'username'    => $userInfo['username'],
            'user_type'   => $userInfo['user_type'],
            'portal'      => $portalContext['portal'],
            'module'      => $portalContext['module'],
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => $request->getUserAgent()->getAgentString(),
            'session_id'  => $session->session_id ?? null,
            'request_url' => current_url(),
            'description' => $this->generateDescription($action, $pk),
        ]);

        return $data; // MUST return to keep the normal pipeline flowing
    }

    /**
     * Detect which portal context we're currently in
     */
    protected function detectPortalContext(): ?array
    {
        $session = session();
        $request = Services::request();
        $uri = $request->getUri()->getPath();

        // Check for Dakoii portal
        if ($session->get('dakoii_user_id') || strpos($uri, '/dakoii/') !== false) {
            return [
                'portal' => 'dakoii',
                'module' => $this->extractModuleFromUrl($uri, 'dakoii')
            ];
        }

        // Check for Admin portal
        if ($session->get('admin_user_id') || strpos($uri, '/admin/') !== false) {
            return [
                'portal' => 'admin',
                'module' => $this->extractModuleFromUrl($uri, 'admin')
            ];
        }

        // Check for Monitor portal
        if ($session->get('monitor_user_id') || strpos($uri, '/monitor/') !== false) {
            return [
                'portal' => 'monitor',
                'module' => $this->extractModuleFromUrl($uri, 'monitor')
            ];
        }

        return null;
    }

    /**
     * Get user information based on portal context
     */
    protected function getUserInfo(array $portalContext, $session): array
    {
        switch ($portalContext['portal']) {
            case 'dakoii':
                return [
                    'user_id' => $session->get('dakoii_user_id'),
                    'username' => $session->get('dakoii_username') ?? 'system',
                    'user_type' => 'dakoii_user'
                ];
            case 'admin':
                return [
                    'user_id' => $session->get('admin_user_id'),
                    'username' => $session->get('admin_username') ?? 'system',
                    'user_type' => 'admin_user'
                ];
            case 'monitor':
                return [
                    'user_id' => $session->get('monitor_user_id'),
                    'username' => $session->get('monitor_username') ?? 'system',
                    'user_type' => 'project_officer'
                ];
            default:
                return [
                    'user_id' => null,
                    'username' => 'system',
                    'user_type' => 'system'
                ];
        }
    }

    /**
     * Extract module name from URL
     */
    protected function extractModuleFromUrl(string $uri, string $portal): ?string
    {
        $pattern = "/{$portal}\/([^\/]+)/";
        if (preg_match($pattern, $uri, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * Generate human-readable description
     */
    protected function generateDescription(string $action, $pk): string
    {
        return "Record {$action} in {$this->table}" . ($pk ? " with ID: {$pk}" : "");
    }
}
```

## 3. Create the Unified Base Model

Create a base model that all auditable models can extend at `app/Models/BaseModel.php`:

```php
<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Traits\Auditable;

/**
 * Base Model for All Auditable Models
 *
 * This base model provides automatic audit logging for all CRUD operations
 * across all portals (Dakoii, Admin, Monitor). It automatically detects
 * the portal context and logs activities accordingly.
 */
class BaseModel extends Model
{
    use Auditable;

    protected $beforeInsert = ['createAudit'];
    protected $beforeUpdate = ['updateAudit'];
    protected $beforeDelete = ['deleteAudit'];

    protected function createAudit(array $data)
    {
        return $this->audit('create', $data);
    }

    protected function updateAudit(array $data)
    {
        return $this->audit('update', $data);
    }

    protected function deleteAudit(array $data)
    {
        return $this->audit('delete', $data);
    }

    /**
     * Skip audit logging for the next operation
     * Useful for bulk operations, seeders, or system operations
     */
    public function skipAudit(): self
    {
        $this->auditSkip = true;
        return $this;
    }
}
```

## 4. Create the Unified Audit Log Model

Create the audit log model for all portals at `app/Models/AuditLogModel.php`:

```php
<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Unified Audit Log Model
 *
 * This model handles audit logs for all portals in the PROMIS application.
 * It provides methods to query audit logs by portal, user, table, etc.
 */
class AuditLogModel extends Model
{
    protected $table      = 'audit_logs';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'table_name', 'primary_key', 'action', 'old_data', 'new_data',
        'user_id', 'username', 'user_type', 'portal', 'module',
        'ip_address', 'user_agent', 'session_id', 'request_url',
        'description', 'created_at',
    ];

    public $timestamps   = false; // we already store created_at manually

    /**
     * Get audit logs for a specific portal
     */
    public function getPortalLogs(string $portal, int $limit = 20): array
    {
        return $this->where('portal', $portal)
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get audit logs for a specific table
     */
    public function getTableLogs(string $tableName, ?string $portal = null, int $limit = 20): array
    {
        $query = $this->where('table_name', $tableName);

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        return $query->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit logs for a specific record
     */
    public function getRecordLogs(string $tableName, string $primaryKey, ?string $portal = null, int $limit = 20): array
    {
        $query = $this->where('table_name', $tableName)
                     ->where('primary_key', $primaryKey);

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        return $query->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit logs by user
     */
    public function getUserLogs(int $userId, string $userType, ?string $portal = null, int $limit = 20): array
    {
        $query = $this->where('user_id', $userId)
                     ->where('user_type', $userType);

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        return $query->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit logs by session
     */
    public function getSessionLogs(string $sessionId, int $limit = 20): array
    {
        return $this->where('session_id', $sessionId)
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get recent activity across all portals
     */
    public function getRecentActivity(int $limit = 50): array
    {
        return $this->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit statistics for a specific portal
     */
    public function getPortalAuditStats(string $portal): array
    {
        $stats = [];

        // Total logs for this portal
        $stats['total_logs'] = $this->where('portal', $portal)->countAllResults();

        // Logs by action
        $stats['by_action'] = $this->select('action, COUNT(*) as count')
                                  ->where('portal', $portal)
                                  ->groupBy('action')
                                  ->findAll();

        // Most active users
        $stats['active_users'] = $this->select('username, user_type, COUNT(*) as count')
                                      ->where('portal', $portal)
                                      ->where('username IS NOT NULL')
                                      ->groupBy('username, user_type')
                                      ->orderBy('count', 'DESC')
                                      ->findAll(10);

        // Most modified tables
        $stats['active_tables'] = $this->select('table_name, COUNT(*) as count')
                                       ->where('portal', $portal)
                                       ->groupBy('table_name')
                                       ->orderBy('count', 'DESC')
                                       ->findAll(10);

        // Activity by module
        $stats['by_module'] = $this->select('module, COUNT(*) as count')
                                  ->where('portal', $portal)
                                  ->where('module IS NOT NULL')
                                  ->groupBy('module')
                                  ->orderBy('count', 'DESC')
                                  ->findAll();

        return $stats;
    }

    /**
     * Get cross-portal audit statistics
     */
    public function getCrossPortalStats(): array
    {
        $stats = [];

        // Total logs across all portals
        $stats['total_logs'] = $this->countAllResults();

        // Logs by portal
        $stats['by_portal'] = $this->select('portal, COUNT(*) as count')
                                  ->groupBy('portal')
                                  ->orderBy('count', 'DESC')
                                  ->findAll();

        // Logs by action across all portals
        $stats['by_action'] = $this->select('action, COUNT(*) as count')
                                  ->groupBy('action')
                                  ->orderBy('count', 'DESC')
                                  ->findAll();

        // Most active users across all portals
        $stats['active_users'] = $this->select('username, user_type, portal, COUNT(*) as count')
                                      ->where('username IS NOT NULL')
                                      ->groupBy('username, user_type, portal')
                                      ->orderBy('count', 'DESC')
                                      ->findAll(15);

        return $stats;
    }
}
```

## 5. Update Models to Use BaseModel

Now every domain model that **extends BaseModel** gains automatic audit logging across all portals. Update your existing models:

```php
<?php

namespace App\Models;

/**
 * Dakoii User Model
 * Extends BaseModel to get automatic audit logging
 */
class DakoiiUserModel extends BaseModel
{
    protected $table      = 'dakoii_users';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'username', 'email', 'password', 'role', 'status',
        'first_name', 'last_name', 'phone', 'organization_id'
    ];
    // ... rest of existing model code
}
```

```php
<?php

namespace App\Models;

/**
 * Admin User Model
 * Extends BaseModel to get automatic audit logging
 */
class AdminUserModel extends BaseModel
{
    protected $table      = 'admin_users';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'username', 'email', 'password', 'role', 'status',
        'first_name', 'last_name', 'phone', 'department'
    ];
    // ... rest of existing model code
}
```

```php
<?php

namespace App\Models;

/**
 * Project Model
 * Extends BaseModel to get automatic audit logging across all portals
 */
class ProjectModel extends BaseModel
{
    protected $table      = 'projects';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'title', 'description', 'status', 'budget',
        'start_date', 'end_date', 'organization_id'
    ];
    // ... rest of existing model code
}
```

```php
<?php

namespace App\Models;

/**
 * Organization Model
 * Extends BaseModel to get automatic audit logging when modified from any portal
 */
class OrganizationModel extends BaseModel
{
    protected $table      = 'dakoii_organizations';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'name', 'type', 'description', 'status',
        'country_id', 'province_id', 'district_id', 'llg_id'
    ];
    // ... rest of existing model code
}
```

**Models that should extend BaseModel** (for audit logging):
- DakoiiUserModel
- AdminUserModel
- ProjectModel
- OrganizationModel
- ContractorModel
- ProjectMilestoneModel
- ProjectBudgetModel
- And any other models that represent business data

**Models that should NOT extend BaseModel** (reference/lookup data):
- CountryModel (static reference data)
- ProvinceModel (static reference data)
- DistrictModel (static reference data)
- LlgModel (static reference data)

**Note**: The unified audit system will automatically detect which portal the change came from and log it appropriately.

## 6. Create a Unified Audit Service (Optional - for Manual Logging)

For cases where you need to log events that aren't captured by model callbacks (like authentication events), create a unified service at `app/Libraries/AuditService.php`:

```php
<?php

namespace App\Libraries;

use App\Models\AuditLogModel;
use Config\Services;

/**
 * Unified Audit Service
 *
 * This service handles manual audit logging for all portals in the PROMIS application.
 * It automatically detects the portal context and logs activities accordingly.
 */
class AuditService
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }

    /**
     * Log authentication events manually
     */
    public function logAuthentication(string $action, string $description, ?int $userId = null): bool
    {
        $request = Services::request();
        $session = session();
        $portalContext = $this->detectPortalContext();

        if (!$portalContext) {
            return false; // No valid portal context
        }

        $userInfo = $this->getUserInfo($portalContext, $session);

        $data = [
            'table_name'  => $portalContext['portal'] . '_users',
            'primary_key' => (string) ($userId ?? $userInfo['user_id'] ?? 'unknown'),
            'action'      => $action,
            'old_data'    => null,
            'new_data'    => json_encode([
                'description' => $description,
                'event_type' => 'authentication'
            ], JSON_UNESCAPED_UNICODE),
            'user_id'     => $userId ?? $userInfo['user_id'],
            'username'    => $userInfo['username'],
            'user_type'   => $userInfo['user_type'],
            'portal'      => $portalContext['portal'],
            'module'      => 'authentication',
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => $request->getUserAgent()->getAgentString(),
            'session_id'  => $session->session_id ?? null,
            'request_url' => current_url(),
            'description' => $description,
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log system events manually
     */
    public function logSystemEvent(string $tableName, string $action, string $description, ?string $portal = null): bool
    {
        $request = Services::request();
        $session = session();
        $portalContext = $portal ? ['portal' => $portal, 'module' => 'system'] : $this->detectPortalContext();

        if (!$portalContext) {
            $portalContext = ['portal' => 'system', 'module' => 'system'];
        }

        $userInfo = $this->getUserInfo($portalContext, $session);

        $data = [
            'table_name'  => $tableName,
            'primary_key' => 'system',
            'action'      => $action,
            'old_data'    => null,
            'new_data'    => json_encode([
                'description' => $description,
                'event_type' => 'system'
            ], JSON_UNESCAPED_UNICODE),
            'user_id'     => $userInfo['user_id'],
            'username'    => $userInfo['username'],
            'user_type'   => $userInfo['user_type'],
            'portal'      => $portalContext['portal'],
            'module'      => $portalContext['module'],
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => $request->getUserAgent()->getAgentString(),
            'session_id'  => $session->session_id ?? null,
            'request_url' => current_url(),
            'description' => $description,
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log portal access events
     */
    public function logPortalAccess(string $page, string $action = 'access'): bool
    {
        $request = Services::request();
        $session = session();
        $portalContext = $this->detectPortalContext();

        if (!$portalContext) {
            return false; // No valid portal context
        }

        $userInfo = $this->getUserInfo($portalContext, $session);

        $data = [
            'table_name'  => $portalContext['portal'] . '_portal_access',
            'primary_key' => $page,
            'action'      => $action,
            'old_data'    => null,
            'new_data'    => json_encode([
                'page' => $page,
                'url' => current_url(),
                'event_type' => 'portal_access'
            ], JSON_UNESCAPED_UNICODE),
            'user_id'     => $userInfo['user_id'],
            'username'    => $userInfo['username'],
            'user_type'   => $userInfo['user_type'],
            'portal'      => $portalContext['portal'],
            'module'      => $portalContext['module'] ?? 'access',
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => $request->getUserAgent()->getAgentString(),
            'session_id'  => $session->session_id ?? null,
            'request_url' => current_url(),
            'description' => "User accessed {$page} page",
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Detect portal context (same as in Auditable trait)
     */
    protected function detectPortalContext(): ?array
    {
        $session = session();
        $request = Services::request();
        $uri = $request->getUri()->getPath();

        // Check for Dakoii portal
        if ($session->get('dakoii_user_id') || strpos($uri, '/dakoii/') !== false) {
            return [
                'portal' => 'dakoii',
                'module' => $this->extractModuleFromUrl($uri, 'dakoii')
            ];
        }

        // Check for Admin portal
        if ($session->get('admin_user_id') || strpos($uri, '/admin/') !== false) {
            return [
                'portal' => 'admin',
                'module' => $this->extractModuleFromUrl($uri, 'admin')
            ];
        }

        // Check for Monitor portal
        if ($session->get('monitor_user_id') || strpos($uri, '/monitor/') !== false) {
            return [
                'portal' => 'monitor',
                'module' => $this->extractModuleFromUrl($uri, 'monitor')
            ];
        }

        return null;
    }

    /**
     * Get user information based on portal context (same as in Auditable trait)
     */
    protected function getUserInfo(array $portalContext, $session): array
    {
        switch ($portalContext['portal']) {
            case 'dakoii':
                return [
                    'user_id' => $session->get('dakoii_user_id'),
                    'username' => $session->get('dakoii_username') ?? 'system',
                    'user_type' => 'dakoii_user'
                ];
            case 'admin':
                return [
                    'user_id' => $session->get('admin_user_id'),
                    'username' => $session->get('admin_username') ?? 'system',
                    'user_type' => 'admin_user'
                ];
            case 'monitor':
                return [
                    'user_id' => $session->get('monitor_user_id'),
                    'username' => $session->get('monitor_username') ?? 'system',
                    'user_type' => 'project_officer'
                ];
            default:
                return [
                    'user_id' => null,
                    'username' => 'system',
                    'user_type' => 'system'
                ];
        }
    }

    /**
     * Extract module name from URL
     */
    protected function extractModuleFromUrl(string $uri, string $portal): ?string
    {
        $pattern = "/{$portal}\/([^\/]+)/";
        if (preg_match($pattern, $uri, $matches)) {
            return $matches[1];
        }
        return null;
    }
}
```

## 7. Register the Unified Audit Service (Optional)

Update your `app/Config/Services.php` file to include the unified audit service:

```php
/**
 * Unified Audit Service
 * Handles audit logging for all portals in the PROMIS application
 */
public static function auditService(bool $getShared = true)
{
    if ($getShared) {
        return static::getSharedInstance('auditService');
    }

    return new \App\Libraries\AuditService();
}
```

## 8. Add Manual Audit Logging to Controllers

For authentication events and other actions not captured by model callbacks, update your controllers across all portals:

### Dakoii Portal Controllers

For example, in `DakoiiAuthController.php`:

```php
// In authenticateUser method, after successful login:
$auditService = \Config\Services::auditService();
$auditService->logAuthentication(
    'login',
    "Dakoii user {$user['username']} logged in successfully",
    $user['id']
);

// In logoutUser method:
$auditService = \Config\Services::auditService();
if ($userId) {
    $auditService->logAuthentication(
        'logout',
        "Dakoii user {$username} logged out",
        $userId
    );
}

// For password reset events:
$auditService->logAuthentication(
    'password_reset',
    "Password reset requested for Dakoii user {$username}",
    $userId
);

// For failed login attempts:
$auditService->logAuthentication(
    'login_failed',
    "Failed login attempt for username: {$username}",
    null
);

// For portal access logging (optional):
$auditService->logPortalAccess('dashboard');
$auditService->logPortalAccess('organizations');
```

### Admin Portal Controllers

For example, in `AdminAuthController.php`:

```php
// In authenticateUser method, after successful login:
$auditService = \Config\Services::auditService();
$auditService->logAuthentication(
    'login',
    "Admin user {$user['username']} logged in successfully",
    $user['id']
);

// In logoutUser method:
$auditService = \Config\Services::auditService();
if ($userId) {
    $auditService->logAuthentication(
        'logout',
        "Admin user {$username} logged out",
        $userId
    );
}

// For project creation:
$auditService->logSystemEvent(
    'projects',
    'create',
    "New project '{$projectTitle}' created by admin {$username}"
);
```

### Monitor Portal Controllers

For example, in `MonitorAuthController.php`:

```php
// In authenticateUser method, after successful login:
$auditService = \Config\Services::auditService();
$auditService->logAuthentication(
    'login',
    "Project officer {$user['username']} logged in successfully",
    $user['id']
);

// For milestone updates:
$auditService->logSystemEvent(
    'project_milestones',
    'update',
    "Milestone progress updated by project officer {$username}"
);
```

## 9. Create Audit Trail Interfaces

### Unified Audit Controller

Create a main audit controller at `app/Controllers/AuditController.php` for system-wide audit viewing:

```php
<?php

namespace App\Controllers;

use App\Models\AuditLogModel;

class AuditController extends BaseController
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }

    public function index()
    {
        // Ensure user has proper permissions (admin or super admin)
        if (!$this->hasAuditPermission()) {
            return redirect()->to(base_url('dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $portal = $this->request->getVar('portal');
        $tableName = $this->request->getVar('table_name');
        $user = $this->request->getVar('user');
        $action = $this->request->getVar('action');
        $dateFrom = $this->request->getVar('date_from');
        $dateTo = $this->request->getVar('date_to');

        // Build query with filters
        $query = $this->auditLogModel;

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        if ($tableName) {
            $query = $query->where('table_name', $tableName);
        }

        if ($user) {
            $query = $query->like('username', $user);
        }

        if ($action) {
            $query = $query->where('action', $action);
        }

        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }

        // Get paginated results
        $logs = $query->orderBy('created_at', 'DESC')
                      ->paginate(20, 'audit');

        $data = [
            'title' => 'System Audit Trail',
            'logs' => $logs,
            'pager' => $this->auditLogModel->pager,
            'portals' => $this->getUniquePortals(),
            'tables' => $this->getUniqueTables(),
            'actions' => $this->getUniqueActions(),
            'filters' => [
                'portal' => $portal,
                'table_name' => $tableName,
                'user' => $user,
                'action' => $action,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ];

        return view('audit/audit_logs', $data);
    }

    public function view($id)
    {
        if (!$this->hasAuditPermission()) {
            return redirect()->to(base_url('dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $log = $this->auditLogModel->find($id);

        if (!$log) {
            return redirect()->to(base_url('audit'))
                ->with('error', 'Audit log entry not found.');
        }

        $data = [
            'title' => 'Audit Log Details',
            'log' => $log
        ];

        return view('audit/audit_log_detail', $data);
    }

    public function stats()
    {
        if (!$this->hasAuditPermission()) {
            return redirect()->to(base_url('dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $data = [
            'title' => 'Audit Statistics',
            'cross_portal_stats' => $this->auditLogModel->getCrossPortalStats(),
            'dakoii_stats' => $this->auditLogModel->getPortalAuditStats('dakoii'),
            'admin_stats' => $this->auditLogModel->getPortalAuditStats('admin'),
            'monitor_stats' => $this->auditLogModel->getPortalAuditStats('monitor'),
        ];

        return view('audit/audit_stats', $data);
    }

    private function hasAuditPermission(): bool
    {
        $session = session();

        // Check if user is admin in any portal
        if ($session->get('admin_user_id') && in_array($session->get('admin_role'), ['admin', 'super_admin'])) {
            return true;
        }

        // Check if user is moderator in Dakoii portal
        if ($session->get('dakoii_user_id') && in_array($session->get('dakoii_role'), ['admin', 'moderator'])) {
            return true;
        }

        return false;
    }

    private function getUniquePortals()
    {
        return $this->auditLogModel->select('portal')
                                  ->distinct()
                                  ->orderBy('portal', 'ASC')
                                  ->findAll();
    }

    private function getUniqueTables()
    {
        return $this->auditLogModel->select('table_name')
                                  ->distinct()
                                  ->orderBy('table_name', 'ASC')
                                  ->findAll();
    }

    private function getUniqueActions()
    {
        return $this->auditLogModel->select('action')
                                  ->distinct()
                                  ->orderBy('action', 'ASC')
                                  ->findAll();
    }
}
```

### Portal-Specific Audit Controllers

Create portal-specific audit controllers for easier access within each portal:

#### Dakoii Portal Audit Controller

Create a controller for Dakoii portal at `app/Controllers/DakoiiAuditController.php`:

```php
<?php

namespace App\Controllers;

use App\Models\AuditLogModel;

class DakoiiAuditController extends BaseController
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }

    public function index()
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('dakoii_role'), ['admin', 'moderator'])) {
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $tableName = $this->request->getVar('table_name');
        $user = $this->request->getVar('user');
        $action = $this->request->getVar('action');
        $dateFrom = $this->request->getVar('date_from');
        $dateTo = $this->request->getVar('date_to');

        // Build query with filters - only Dakoii portal logs
        $query = $this->auditLogModel->where('portal', 'dakoii');

        if ($tableName) {
            $query = $query->where('table_name', $tableName);
        }

        if ($user) {
            $query = $query->like('username', $user);
        }

        if ($action) {
            $query = $query->where('action', $action);
        }

        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }

        // Get paginated results
        $logs = $query->orderBy('created_at', 'DESC')
                      ->paginate(20, 'dakoii_audit');

        $data = [
            'title' => 'Dakoii Portal Audit Trail',
            'logs' => $logs,
            'pager' => $this->auditLogModel->pager,
            'tables' => $this->getDakoiiUniqueTables(),
            'actions' => $this->getDakoiiUniqueActions(),
            'filters' => [
                'table_name' => $tableName,
                'user' => $user,
                'action' => $action,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ];

        return view('dakoii/dakoii_audit_logs', $data);
    }

    private function getDakoiiUniqueTables()
    {
        return $this->auditLogModel->select('table_name')
                                  ->where('portal', 'dakoii')
                                  ->distinct()
                                  ->orderBy('table_name', 'ASC')
                                  ->findAll();
    }

    private function getDakoiiUniqueActions()
    {
        return $this->auditLogModel->select('action')
                                  ->where('portal', 'dakoii')
                                  ->distinct()
                                  ->orderBy('action', 'ASC')
                                  ->findAll();
    }

    public function view($id)
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('dakoii_role'), ['admin', 'moderator'])) {
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $log = $this->auditLogModel->find($id);

        // Ensure log belongs to Dakoii portal
        if (!$log || $log['portal'] !== 'dakoii') {
            return redirect()->to(base_url('dakoii/audit'))
                ->with('error', 'Audit log entry not found.');
        }

        $data = [
            'title' => 'Dakoii Audit Log Details',
            'log' => $log
        ];

        return view('dakoii/dakoii_audit_log_detail', $data);
    }
}
```

#### Admin Portal Audit Controller

Create a controller for Admin portal at `app/Controllers/AdminAuditController.php`:

```php
<?php

namespace App\Controllers;

use App\Models\AuditLogModel;

class AdminAuditController extends BaseController
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }

    public function index()
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('admin_role'), ['admin', 'super_admin'])) {
            return redirect()->to(base_url('admin/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $tableName = $this->request->getVar('table_name');
        $user = $this->request->getVar('user');
        $action = $this->request->getVar('action');
        $dateFrom = $this->request->getVar('date_from');
        $dateTo = $this->request->getVar('date_to');

        // Build query with filters - only Admin portal logs
        $query = $this->auditLogModel->where('portal', 'admin');

        if ($tableName) {
            $query = $query->where('table_name', $tableName);
        }

        if ($user) {
            $query = $query->like('username', $user);
        }

        if ($action) {
            $query = $query->where('action', $action);
        }

        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }

        // Get paginated results
        $logs = $query->orderBy('created_at', 'DESC')
                      ->paginate(20, 'admin_audit');

        $data = [
            'title' => 'Admin Portal Audit Trail',
            'logs' => $logs,
            'pager' => $this->auditLogModel->pager,
            'tables' => $this->getAdminUniqueTables(),
            'actions' => $this->getAdminUniqueActions(),
            'filters' => [
                'table_name' => $tableName,
                'user' => $user,
                'action' => $action,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ];

        return view('admin/admin_audit_logs', $data);
    }

    private function getAdminUniqueTables()
    {
        return $this->auditLogModel->select('table_name')
                                  ->where('portal', 'admin')
                                  ->distinct()
                                  ->orderBy('table_name', 'ASC')
                                  ->findAll();
    }

    private function getAdminUniqueActions()
    {
        return $this->auditLogModel->select('action')
                                  ->where('portal', 'admin')
                                  ->distinct()
                                  ->orderBy('action', 'ASC')
                                  ->findAll();
    }

    public function view($id)
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('admin_role'), ['admin', 'super_admin'])) {
            return redirect()->to(base_url('admin/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $log = $this->auditLogModel->find($id);

        // Ensure log belongs to Admin portal
        if (!$log || $log['portal'] !== 'admin') {
            return redirect()->to(base_url('admin/audit'))
                ->with('error', 'Audit log entry not found.');
        }

        $data = [
            'title' => 'Admin Audit Log Details',
            'log' => $log
        ];

        return view('admin/admin_audit_log_detail', $data);
    }
}
```

## 10. Create Audit Log Views

Create a view file at `app/Views/dakoii/dakoii_audit_logs.php`:

```php
<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Audit Trail</h3>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Table</label>
                                    <select name="table_name" class="form-control">
                                        <option value="">All Tables</option>
                                        <?php foreach ($tables as $table): ?>
                                        <option value="<?= $table['table_name'] ?>" <?= ($filters['table_name'] == $table['table_name']) ? 'selected' : '' ?>>
                                            <?= ucfirst(str_replace('_', ' ', $table['table_name'])) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Action</label>
                                    <select name="action" class="form-control">
                                        <option value="">All Actions</option>
                                        <?php foreach ($actions as $act): ?>
                                        <option value="<?= $act['action'] ?>" <?= ($filters['action'] == $act['action']) ? 'selected' : '' ?>>
                                            <?= ucfirst($act['action']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Username</label>
                                    <input type="text" name="user" class="form-control" value="<?= $filters['user'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Date From</label>
                                    <input type="date" name="date_from" class="form-control" value="<?= $filters['date_from'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Date To</label>
                                    <input type="date" name="date_to" class="form-control" value="<?= $filters['date_to'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">Filter</button>
                                        <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-secondary">Reset</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Audit Logs Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>User</th>
                                    <th>Table</th>
                                    <th>Action</th>
                                    <th>Record ID</th>
                                    <th>IP Address</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($logs)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">No audit logs found</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></td>
                                        <td><?= esc($log['dakoii_username']) ?></td>
                                        <td><?= ucfirst(str_replace('_', ' ', esc($log['table_name']))) ?></td>
                                        <td>
                                            <span class="badge badge-<?= $log['action'] === 'create' ? 'success' : ($log['action'] === 'update' ? 'warning' : 'danger') ?>">
                                                <?= ucfirst(esc($log['action'])) ?>
                                            </span>
                                        </td>
                                        <td><?= esc($log['primary_key']) ?></td>
                                        <td><?= esc($log['ip_address']) ?></td>
                                        <td>
                                            <a href="<?= base_url('dakoii/audit/view/' . $log['id']) ?>" class="btn btn-sm btn-info">
                                                Details
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-3">
                        <?= $pager->links('audit', 'bootstrap_pagination') ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

Create a view for detailed audit log entries at `app/Views/dakoii/dakoii_audit_log_detail.php`:

```php
<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Audit Log Details</h3>
                    <div class="card-tools">
                        <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>General Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">Timestamp</th>
                                    <td><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Dakoii User</th>
                                    <td><?= esc($log['dakoii_username']) ?> (ID: <?= $log['dakoii_user_id'] ?: 'N/A' ?>)</td>
                                </tr>
                                <tr>
                                    <th>IP Address</th>
                                    <td><?= esc($log['ip_address']) ?></td>
                                </tr>
                                <tr>
                                    <th>Table</th>
                                    <td><?= ucfirst(str_replace('_', ' ', esc($log['table_name']))) ?></td>
                                </tr>
                                <tr>
                                    <th>Action</th>
                                    <td>
                                        <span class="badge badge-<?= $log['action'] === 'create' ? 'success' : ($log['action'] === 'update' ? 'warning' : 'danger') ?>">
                                            <?= ucfirst(esc($log['action'])) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Record ID</th>
                                    <td><?= esc($log['primary_key']) ?></td>
                                </tr>
                                <tr>
                                    <th>Session ID</th>
                                    <td><?= esc($log['session_id']) ?: 'N/A' ?></td>
                                </tr>
                                <tr>
                                    <th>Portal Context</th>
                                    <td><span class="badge badge-primary"><?= ucfirst(esc($log['portal_context'])) ?></span></td>
                                </tr>
                                <tr>
                                    <th>User Agent</th>
                                    <td><small><?= esc($log['user_agent']) ?></small></td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <?php if ($log['old_data'] || $log['new_data']): ?>
                                <h5>Data Changes</h5>

                                <?php if ($log['old_data']): ?>
                                    <h6 class="mt-3">Old Values</h6>
                                    <div class="p-3 bg-light">
                                        <pre><?= json_encode(json_decode($log['old_data']), JSON_PRETTY_PRINT) ?></pre>
                                    </div>
                                <?php endif; ?>

                                <?php if ($log['new_data']): ?>
                                    <h6 class="mt-3">New Values</h6>
                                    <div class="p-3 bg-light">
                                        <pre><?= json_encode(json_decode($log['new_data']), JSON_PRETTY_PRINT) ?></pre>
                                    </div>
                                <?php endif; ?>

                                <?php if ($log['old_data'] && $log['new_data']): ?>
                                    <h6 class="mt-3">Field Changes</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Field</th>
                                                    <th>Old Value</th>
                                                    <th>New Value</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $oldData = json_decode($log['old_data'], true);
                                                $newData = json_decode($log['new_data'], true);
                                                $allFields = array_unique(array_merge(array_keys($oldData ?: []), array_keys($newData ?: [])));
                                                foreach ($allFields as $field):
                                                    $oldValue = $oldData[$field] ?? 'N/A';
                                                    $newValue = $newData[$field] ?? 'N/A';
                                                    if ($oldValue !== $newValue):
                                                ?>
                                                <tr>
                                                    <td><strong><?= esc($field) ?></strong></td>
                                                    <td><code><?= esc($oldValue) ?></code></td>
                                                    <td><code><?= esc($newValue) ?></code></td>
                                                </tr>
                                                <?php endif; endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

## 11. Update Routes for Dakoii Portal

Add the Dakoii audit routes in `app/Config/Routes.php`:

```php
// Dakoii Portal Audit Trail routes
$routes->group('dakoii', ['filter' => 'dakoiiAuth'], function ($routes) {
    $routes->get('audit', 'DakoiiAuditController::index');
    $routes->get('audit/view/(:num)', 'DakoiiAuditController::view/$1');
    $routes->get('audit/stats', 'DakoiiAuditController::stats'); // Optional: audit statistics
});
```

## 12. Add to Dakoii Portal Navigation

Update your Dakoii portal navigation menu to include the audit trail link:

```php
<!-- In your dakoii_portal_template.php file -->
<li class="nav-item">
    <a href="<?= base_url('dakoii/audit') ?>" class="nav-link <?= (strpos(current_url(), 'audit') !== false) ? 'active' : '' ?>">
        <i class="nav-icon fas fa-history"></i>
        <p>Dakoii Audit Trail</p>
    </a>
</li>
```

**Note**: This audit trail link should only appear in the Dakoii portal navigation, not in other portals.

## Advanced Features and Tweaks

### Skip Dakoii Auditing for Specific Operations

```php
// Skip Dakoii auditing in seeder/factory code
$userModel = new DakoiiUserModel();
$userModel->skipDakoiiAudit()->insert($userData);

// Or for bulk operations
$userModel->skipDakoiiAudit()->insertBatch($bulkUserData);
```

### Portal-Specific Model Architecture

The recommended approach is to have separate base models for each portal:

```php
<?php

namespace App\Models;

// For Dakoii Portal models
class DakoiiUserModel extends DakoiiBaseModel
{
    // Automatically gets Dakoii audit logging
}

// For Admin Portal models (to be created)
class AdminUserModel extends AdminBaseModel
{
    // Would get Admin audit logging
}

// For Project Monitoring models (to be created)
class ProjectModel extends ProjectBaseModel
{
    // Would get Project audit logging
}

// For shared/reference models (no specific portal audit)
class CountryModel extends Model
{
    // No automatic audit logging - shared across portals
}
```

### Log Dakoii SELECT Queries (Optional)

If you need to log read operations specifically for Dakoii portal, add this to `app/Config/Events.php`:

```php
Events::on('DBQuery', static function($query) {
    if (stripos($query->getQuery(), 'select') === 0) {
        $session = session();

        // Only log if user is in Dakoii portal context
        if ($session->get('dakoii_user_id')) {
            $auditModel = new \App\Models\DakoiiAuditLogModel();
            $request = \Config\Services::request();

            $auditModel->insert([
                'table_name'      => 'system',
                'primary_key'     => 'query',
                'action'          => 'read',
                'old_data'        => null,
                'new_data'        => json_encode(['query' => $query->getQuery()]),
                'dakoii_user_id'  => $session->get('dakoii_user_id'),
                'dakoii_username' => $session->get('dakoii_username') ?? 'system',
                'ip_address'      => $request->getIPAddress(),
                'user_agent'      => $request->getUserAgent()->getAgentString(),
                'session_id'      => $session->session_id ?? null,
                'portal_context'  => 'dakoii',
            ]);
        }
    }
});
```

**Warning**: This will log ALL SELECT queries when a Dakoii user is logged in, which can generate a lot of audit data. Use with caution.

## Implementation Benefits

This comprehensive unified audit trail implementation provides:

1. **Unified auditing** – single source of truth for all application activities
2. **Cross-portal visibility** – see activities across Dakoii, Admin, and Monitor portals
3. **Zero boilerplate per model** – just extend `BaseModel`
4. **No custom DB drivers** – pure CI4 model callbacks
5. **Single table approach** simplifies reporting and maintenance
6. **Context-aware auditing** – automatically detects portal context
7. **Trait flag** lets you disable auditing case-by-case (bulk imports, tests, etc.)
8. **Automatic CRUD tracking** for all database operations across all portals
9. **Manual logging capability** for authentication and system events
10. **Detailed change tracking** with before/after values
11. **Portal-specific interfaces** while maintaining unified backend
12. **Cross-portal analytics** and reporting capabilities
13. **User-friendly interfaces** to browse, filter, and search audit logs
14. **Proper security** with role-based access control
15. **Battle-tested pattern** that's been proven in production environments
16. **Simplified maintenance** – one audit system to maintain instead of three
17. **Better correlation** – can track user activities across multiple portals

## Implementation Checklist

### Core Implementation
- [ ] Create `audit_logs` table using migration (unified table)
- [ ] Create `Auditable` trait (`app/Traits/Auditable.php`)
- [ ] Create `BaseModel` class (`app/Models/BaseModel.php`)
- [ ] Create `AuditLogModel` (`app/Models/AuditLogModel.php`)
- [ ] Create `AuditService` (optional, `app/Libraries/AuditService.php`)
- [ ] Register unified audit service in `Services.php` (if using service)

### Model Updates
- [ ] Update business models to extend `BaseModel` (DakoiiUserModel, AdminUserModel, ProjectModel, etc.)
- [ ] Keep reference models (Country, Province, etc.) as regular models
- [ ] Test automatic audit logging with CRUD operations

### Controller Updates
- [ ] Add manual audit logging to Dakoii controllers (authentication events)
- [ ] Add manual audit logging to Admin controllers (authentication events)
- [ ] Add manual audit logging to Monitor controllers (authentication events)

### Audit Interfaces
- [ ] Create main `AuditController` (`app/Controllers/AuditController.php`)
- [ ] Create `DakoiiAuditController` (`app/Controllers/DakoiiAuditController.php`)
- [ ] Create `AdminAuditController` (`app/Controllers/AdminAuditController.php`)
- [ ] Create `MonitorAuditController` (optional, `app/Controllers/MonitorAuditController.php`)

### Views and Routes
- [ ] Create unified audit views (`audit/audit_logs.php`, `audit/audit_log_detail.php`, `audit/audit_stats.php`)
- [ ] Create portal-specific audit views (`dakoii/dakoii_audit_logs.php`, `admin/admin_audit_logs.php`)
- [ ] Add audit routes to `Routes.php` for all portals
- [ ] Add audit links to navigation menus in each portal

### Testing and Verification
- [ ] Test the implementation with CRUD operations from all portals
- [ ] Verify audit logs are being created correctly with proper portal identification
- [ ] Test cross-portal audit viewing and filtering
- [ ] Verify portal-specific audit views show only relevant logs
- [ ] Test audit statistics and reporting features
- [ ] Verify role-based access control for audit viewing

## Why This Unified Approach?

This implementation provides the best solution for a multi-portal system:

- **Single source of truth** – all application activities in one place
- **Cross-portal correlation** – track user activities across multiple portals
- **Simplified maintenance** – one audit system instead of three separate systems
- **Better analytics** – comprehensive reporting across the entire application
- **Automatic tracking** through model callbacks ensures nothing is missed
- **Simplified schema** with a single table makes reporting and maintenance easier
- **Battle-tested pattern** that has been proven in production environments
- **CodeIgniter 4 native** approach using built-in features
- **Flexible configuration** allows selective auditing and manual logging
- **Performance optimized** with minimal overhead on normal operations
- **Context-aware** – automatically detects and logs portal context
- **Portal-specific views** while maintaining unified backend storage

## Key Advantages Over Portal-Specific Approach

1. **Reduced Complexity**: One audit system to maintain instead of three
2. **Better Visibility**: See all application activities in one place
3. **Cross-Portal Analytics**: Track user behavior across portals
4. **Simplified Reporting**: Generate reports across the entire application
5. **Easier Troubleshooting**: Follow user actions across portal boundaries
6. **Reduced Storage**: Single table with proper indexing vs. multiple tables
7. **Consistent Implementation**: Same audit pattern across all portals

## Portal Context Detection

The system automatically detects portal context through:
- **Session Variables**: Checks for portal-specific user sessions
- **URL Patterns**: Analyzes request URLs for portal indicators
- **User Types**: Identifies user types (dakoii_user, admin_user, project_officer)

This approach follows modern application architecture patterns while providing a robust, production-ready audit trail capability that enhances security and accountability across the entire PROMIS application system.