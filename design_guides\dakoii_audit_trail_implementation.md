# Dakoii Portal Audit Trail Implementation Guide

This document outlines the implementation of a comprehensive audit trail feature for the Dakoii portal system. The audit trail will track user actions within the portal, which is important for security, accountability, and troubleshooting. This implementation uses a battle-tested pattern that captures **only CRUD activity** (create, read*, update, delete) in a single audit table.

*Note: We normally skip plain "reads" because they don't alter state, but you can easily add them if needed.*

## 1. Database Setup

### Option 1: Using CodeIgniter 4 Migration (Recommended)

Create the migration file:

```bash
php spark make:migration CreateDakoiiAuditLogsTable
```

Update the migration file:

```php
// database/migrations/YYYY-MM-DD-HHMMSS_CreateDakoiiAuditLogsTable.php
<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateDakoiiAuditLogsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id'          => ['type' => 'BIGINT', 'auto_increment' => true],
            'table_name'  => ['type' => 'VARCHAR', 'constraint' => 100],
            'primary_key' => ['type' => 'VARCHAR', 'constraint' => 64],
            'action'      => ['type' => 'ENUM', 'constraint' => ['create','update','delete']],
            'old_data'    => ['type' => 'MEDIUMTEXT', 'null' => true],
            'new_data'    => ['type' => 'MEDIUMTEXT', 'null' => true],
            'user_id'     => ['type' => 'BIGINT', 'null' => true],
            'username'    => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true],
            'ip_address'  => ['type' => 'VARCHAR', 'constraint' => 45, 'null' => true],
            'user_agent'  => ['type' => 'TEXT', 'null' => true],
            'created_at'  => ['type' => 'DATETIME', 'default' => 'CURRENT_TIMESTAMP'],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('table_name');
        $this->forge->addKey('user_id');
        $this->forge->addKey('action');
        $this->forge->addKey('created_at');

        $this->forge->createTable('dakoii_audit_logs');
    }

    public function down()
    {
        $this->forge->dropTable('dakoii_audit_logs');
    }
}
```

Run the migration:

```bash
php spark migrate
```

### Option 2: Direct SQL (Alternative)

```sql
CREATE TABLE dakoii_audit_logs (
    id                BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    table_name        VARCHAR(100) NOT NULL,
    primary_key       VARCHAR(64) NOT NULL,
    action            ENUM('create','update','delete') NOT NULL,
    old_data          MEDIUMTEXT NULL,
    new_data          MEDIUMTEXT NULL,
    user_id           BIGINT UNSIGNED NULL,
    username          VARCHAR(50) NULL,
    ip_address        VARCHAR(45) NULL,
    user_agent        TEXT NULL,
    created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

    /* Indexes */
    INDEX idx_table_name (table_name),
    INDEX idx_user_id    (user_id),
    INDEX idx_action     (action),
    INDEX idx_created_at (created_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;
```

## 2. Create the Auditable Trait

Create a reusable trait at `app/Traits/Auditable.php`:

```php
<?php

namespace App\Traits;

use Config\Services;
use App\Models\DakoiiAuditLogModel;

trait Auditable
{
    protected bool $auditSkip = false;   // allows manual opt-out per call

    // Automatically attached in Model::$beforeInsert / $beforeUpdate / $beforeDelete
    protected function audit(string $action, array $data): array
    {
        if ($this->auditSkip) {
            return $data;
        }   // skip when needed

        $request   = Services::request();
        $session   = session();
        $userId    = $session->get('dakoii_user_id') ?? null;
        $username  = $session->get('dakoii_username') ?? 'system';
        $ip        = $request->getIPAddress();
        $userAgent = $request->getUserAgent()->getAgentString();
        $pkField   = $this->primaryKey;
        $pk        = $data['id'] ?? $data['data'][$pkField] ?? null;

        $old = null;
        $new = null;

        if ($action === 'update') {
            $old = json_encode($this->asArray()
                    ->where($pkField, $pk)
                    ->first(), JSON_UNESCAPED_UNICODE);
            $new = json_encode($data['data'], JSON_UNESCAPED_UNICODE);
        } elseif ($action === 'create') {
            $new = json_encode($data['data'], JSON_UNESCAPED_UNICODE);
        } elseif ($action === 'delete') {
            $old = json_encode($this->asArray()->where($pkField, $pk)->first(), JSON_UNESCAPED_UNICODE);
        }

        (new DakoiiAuditLogModel())->insert([
            'table_name'  => $this->table,
            'primary_key' => (string) $pk,
            'action'      => $action,
            'old_data'    => $old,
            'new_data'    => $new,
            'user_id'     => $userId,
            'username'    => $username,
            'ip_address'  => $ip,
            'user_agent'  => $userAgent,
        ]);

        return $data; // MUST return to keep the normal pipeline flowing
    }
}
```

## 3. Create the Base Model

Create a base model that everyone else extends at `app/Models/BaseModel.php`:

```php
<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Traits\Auditable;

class BaseModel extends Model
{
    use Auditable;

    protected $beforeInsert = ['createAudit'];
    protected $beforeUpdate = ['updateAudit'];
    protected $beforeDelete = ['deleteAudit'];

    protected function createAudit(array $data)
    {
        return $this->audit('create', $data);
    }

    protected function updateAudit(array $data)
    {
        return $this->audit('update', $data);
    }

    protected function deleteAudit(array $data)
    {
        return $this->audit('delete', $data);
    }

    /**
     * Skip audit logging for the next operation
     * Useful for bulk operations, seeders, or system operations
     */
    public function skipAudit(): self
    {
        $this->auditSkip = true;
        return $this;
    }
}
```

## 4. Create the Audit Log Model

Create the audit log model at `app/Models/DakoiiAuditLogModel.php`:

```php
<?php

namespace App\Models;

use CodeIgniter\Model;

class DakoiiAuditLogModel extends Model
{
    protected $table      = 'dakoii_audit_logs';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'table_name', 'primary_key', 'action',
        'old_data', 'new_data', 'user_id', 'username',
        'ip_address', 'user_agent', 'created_at',
    ];

    public $timestamps   = false; // we already store created_at manually

    /**
     * Get audit logs for a specific table
     */
    public function getTableLogs(string $tableName, int $limit = 20): array
    {
        return $this->where('table_name', $tableName)
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get audit logs for a specific record
     */
    public function getRecordLogs(string $tableName, string $primaryKey, int $limit = 20): array
    {
        return $this->where('table_name', $tableName)
                   ->where('primary_key', $primaryKey)
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get audit logs by user
     */
    public function getUserLogs(int $userId, int $limit = 20): array
    {
        return $this->where('user_id', $userId)
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }
}
```

## 5. Update Existing Models to Use BaseModel

Now every domain model that **extends BaseModel** gains audit logging automatically. Update your existing models:

```php
<?php

namespace App\Models;

class DakoiiUserModel extends BaseModel
{
    protected $table      = 'dakoii_users';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'username', 'email', 'password', 'role', 'status',
        'first_name', 'last_name', 'phone', 'organization_id'
    ];
    // ... rest of existing model code
}
```

```php
<?php

namespace App\Models;

class OrganizationModel extends BaseModel
{
    protected $table      = 'organizations';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'name', 'type', 'description', 'status',
        'country_id', 'province_id', 'district_id', 'llg_id'
    ];
    // ... rest of existing model code
}
```

Do the same for other models like:
- CountryModel
- ProvinceModel
- DistrictModel
- LlgModel
- OrganizationImageModel

## 6. Create an Audit Service (Optional - for Manual Logging)

For cases where you need to log events that aren't captured by model callbacks (like authentication events), create a service at `app/Libraries/DakoiiAuditService.php`:

```php
<?php

namespace App\Libraries;

use App\Models\DakoiiAuditLogModel;
use Config\Services;

class DakoiiAuditService
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new DakoiiAuditLogModel();
    }

    /**
     * Log authentication events manually
     */
    public function logAuthentication(string $action, string $description, int $userId = null): bool
    {
        $request = Services::request();
        $session = session();

        $data = [
            'table_name'  => 'dakoii_users',
            'primary_key' => (string) ($userId ?? $session->get('dakoii_user_id') ?? 'unknown'),
            'action'      => $action, // Note: this extends beyond create/update/delete
            'old_data'    => null,
            'new_data'    => json_encode(['description' => $description], JSON_UNESCAPED_UNICODE),
            'user_id'     => $userId ?? $session->get('dakoii_user_id'),
            'username'    => $session->get('dakoii_username') ?? 'system',
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => $request->getUserAgent()->getAgentString(),
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log system events manually
     */
    public function logSystemEvent(string $tableName, string $action, string $description): bool
    {
        $request = Services::request();
        $session = session();

        $data = [
            'table_name'  => $tableName,
            'primary_key' => 'system',
            'action'      => $action,
            'old_data'    => null,
            'new_data'    => json_encode(['description' => $description], JSON_UNESCAPED_UNICODE),
            'user_id'     => $session->get('dakoii_user_id'),
            'username'    => $session->get('dakoii_username') ?? 'system',
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => $request->getUserAgent()->getAgentString(),
        ];

        return $this->auditLogModel->insert($data);
    }
}
```

## 7. Register the Service (Optional)

Update your `app/Config/Services.php` file to include the audit service:

```php
public static function dakoiiAuditService(bool $getShared = true)
{
    if ($getShared) {
        return static::getSharedInstance('dakoiiAuditService');
    }

    return new \App\Libraries\DakoiiAuditService();
}
```

## 8. Add Manual Audit Logging to Controllers

For authentication events and other actions not captured by model callbacks, update your controllers. For example, in `DakoiiAuthController.php`:

```php
// In authenticateUser method, after successful login:
$auditService = \Config\Services::dakoiiAuditService();
$auditService->logAuthentication(
    'login',
    "User {$user['username']} logged in successfully",
    $user['id']
);

// In logoutUser method:
$auditService = \Config\Services::dakoiiAuditService();
if ($userId) {
    $auditService->logAuthentication(
        'logout',
        "User {$username} logged out",
        $userId
    );
}

// For password reset events:
$auditService->logAuthentication(
    'password_reset',
    "Password reset requested for user {$username}",
    $userId
);
```

## 9. Create an Audit Trail Interface

Create a controller to view the audit logs at `app/Controllers/DakoiiAuditController.php`:

```php
<?php

namespace App\Controllers;

use App\Models\DakoiiAuditLogModel;

class DakoiiAuditController extends BaseController
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new DakoiiAuditLogModel();
    }

    public function index()
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('dakoii_role'), ['admin', 'moderator'])) {
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $page = $this->request->getVar('page') ? (int)$this->request->getVar('page') : 1;
        $tableName = $this->request->getVar('table_name');
        $user = $this->request->getVar('user');
        $action = $this->request->getVar('action');
        $dateFrom = $this->request->getVar('date_from');
        $dateTo = $this->request->getVar('date_to');

        // Build query with filters
        $query = $this->auditLogModel;

        if ($tableName) {
            $query = $query->where('table_name', $tableName);
        }

        if ($user) {
            $query = $query->like('username', $user);
        }

        if ($action) {
            $query = $query->where('action', $action);
        }

        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }

        // Get paginated results
        $logs = $query->orderBy('created_at', 'DESC')
                      ->paginate(20, 'audit');

        $data = [
            'title' => 'Audit Trail',
            'logs' => $logs,
            'pager' => $this->auditLogModel->pager,
            'tables' => $this->getUniqueTables(),
            'actions' => $this->getUniqueActions(),
            'filters' => [
                'table_name' => $tableName,
                'user' => $user,
                'action' => $action,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ];

        return view('dakoii/dakoii_audit_logs', $data);
    }

    private function getUniqueTables()
    {
        return $this->auditLogModel->select('table_name')
                                  ->distinct()
                                  ->orderBy('table_name', 'ASC')
                                  ->findAll();
    }

    private function getUniqueActions()
    {
        return $this->auditLogModel->select('action')
                                  ->distinct()
                                  ->orderBy('action', 'ASC')
                                  ->findAll();
    }

    public function view($id)
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('dakoii_role'), ['admin', 'moderator'])) {
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $log = $this->auditLogModel->find($id);

        if (!$log) {
            return redirect()->to(base_url('dakoii/audit'))
                ->with('error', 'Audit log entry not found.');
        }

        $data = [
            'title' => 'Audit Log Details',
            'log' => $log
        ];

        return view('dakoii/dakoii_audit_log_detail', $data);
    }
}
```

## 10. Create Audit Log Views

Create a view file at `app/Views/dakoii/dakoii_audit_logs.php`:

```php
<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Audit Trail</h3>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Table</label>
                                    <select name="table_name" class="form-control">
                                        <option value="">All Tables</option>
                                        <?php foreach ($tables as $table): ?>
                                        <option value="<?= $table['table_name'] ?>" <?= ($filters['table_name'] == $table['table_name']) ? 'selected' : '' ?>>
                                            <?= ucfirst(str_replace('_', ' ', $table['table_name'])) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Action</label>
                                    <select name="action" class="form-control">
                                        <option value="">All Actions</option>
                                        <?php foreach ($actions as $act): ?>
                                        <option value="<?= $act['action'] ?>" <?= ($filters['action'] == $act['action']) ? 'selected' : '' ?>>
                                            <?= ucfirst($act['action']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Username</label>
                                    <input type="text" name="user" class="form-control" value="<?= $filters['user'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Date From</label>
                                    <input type="date" name="date_from" class="form-control" value="<?= $filters['date_from'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Date To</label>
                                    <input type="date" name="date_to" class="form-control" value="<?= $filters['date_to'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">Filter</button>
                                        <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-secondary">Reset</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Audit Logs Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>User</th>
                                    <th>Table</th>
                                    <th>Action</th>
                                    <th>Record ID</th>
                                    <th>IP Address</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($logs)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">No audit logs found</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></td>
                                        <td><?= esc($log['username']) ?></td>
                                        <td><?= ucfirst(str_replace('_', ' ', esc($log['table_name']))) ?></td>
                                        <td>
                                            <span class="badge badge-<?= $log['action'] === 'create' ? 'success' : ($log['action'] === 'update' ? 'warning' : 'danger') ?>">
                                                <?= ucfirst(esc($log['action'])) ?>
                                            </span>
                                        </td>
                                        <td><?= esc($log['primary_key']) ?></td>
                                        <td><?= esc($log['ip_address']) ?></td>
                                        <td>
                                            <a href="<?= base_url('dakoii/audit/view/' . $log['id']) ?>" class="btn btn-sm btn-info">
                                                Details
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-3">
                        <?= $pager->links('audit', 'bootstrap_pagination') ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

Create a view for detailed audit log entries at `app/Views/dakoii/dakoii_audit_log_detail.php`:

```php
<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Audit Log Details</h3>
                    <div class="card-tools">
                        <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>General Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">Timestamp</th>
                                    <td><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <th>User</th>
                                    <td><?= esc($log['username']) ?> (ID: <?= $log['user_id'] ?: 'N/A' ?>)</td>
                                </tr>
                                <tr>
                                    <th>IP Address</th>
                                    <td><?= esc($log['ip_address']) ?></td>
                                </tr>
                                <tr>
                                    <th>Table</th>
                                    <td><?= ucfirst(str_replace('_', ' ', esc($log['table_name']))) ?></td>
                                </tr>
                                <tr>
                                    <th>Action</th>
                                    <td>
                                        <span class="badge badge-<?= $log['action'] === 'create' ? 'success' : ($log['action'] === 'update' ? 'warning' : 'danger') ?>">
                                            <?= ucfirst(esc($log['action'])) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Record ID</th>
                                    <td><?= esc($log['primary_key']) ?></td>
                                </tr>
                                <tr>
                                    <th>User Agent</th>
                                    <td><small><?= esc($log['user_agent']) ?></small></td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <?php if ($log['old_data'] || $log['new_data']): ?>
                                <h5>Data Changes</h5>

                                <?php if ($log['old_data']): ?>
                                    <h6 class="mt-3">Old Values</h6>
                                    <div class="p-3 bg-light">
                                        <pre><?= json_encode(json_decode($log['old_data']), JSON_PRETTY_PRINT) ?></pre>
                                    </div>
                                <?php endif; ?>

                                <?php if ($log['new_data']): ?>
                                    <h6 class="mt-3">New Values</h6>
                                    <div class="p-3 bg-light">
                                        <pre><?= json_encode(json_decode($log['new_data']), JSON_PRETTY_PRINT) ?></pre>
                                    </div>
                                <?php endif; ?>

                                <?php if ($log['old_data'] && $log['new_data']): ?>
                                    <h6 class="mt-3">Field Changes</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Field</th>
                                                    <th>Old Value</th>
                                                    <th>New Value</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $oldData = json_decode($log['old_data'], true);
                                                $newData = json_decode($log['new_data'], true);
                                                $allFields = array_unique(array_merge(array_keys($oldData ?: []), array_keys($newData ?: [])));
                                                foreach ($allFields as $field):
                                                    $oldValue = $oldData[$field] ?? 'N/A';
                                                    $newValue = $newData[$field] ?? 'N/A';
                                                    if ($oldValue !== $newValue):
                                                ?>
                                                <tr>
                                                    <td><strong><?= esc($field) ?></strong></td>
                                                    <td><code><?= esc($oldValue) ?></code></td>
                                                    <td><code><?= esc($newValue) ?></code></td>
                                                </tr>
                                                <?php endif; endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

## 11. Update Routes

Add the audit routes in `app/Config/Routes.php`:

```php
// Audit Trail routes
$routes->group('dakoii', ['filter' => 'dakoiiAuth'], function ($routes) {
    $routes->get('audit', 'DakoiiAuditController::index');
    $routes->get('audit/view/(:num)', 'DakoiiAuditController::view/$1');
});
```

## 12. Add to Navigation

Update your portal navigation menu to include the audit trail link:

```php
<!-- In your dakoii_portal_template.php file -->
<li class="nav-item">
    <a href="<?= base_url('dakoii/audit') ?>" class="nav-link <?= (strpos(current_url(), 'audit') !== false) ? 'active' : '' ?>">
        <i class="nav-icon fas fa-history"></i>
        <p>Audit Trail</p>
    </a>
</li>
```

## Advanced Features and Tweaks

### Skip Auditing for Specific Operations

```php
// Skip auditing in seeder/factory code
$userModel = new DakoiiUserModel();
$userModel->skipAudit()->insert($userData);

// Or for bulk operations
$userModel->skipAudit()->insertBatch($bulkUserData);
```

### Only Audit Specific Models

If you don't want all models to be audited, remove the `Auditable` trait from `BaseModel` and add it selectively:

```php
<?php

namespace App\Models;

use App\Traits\Auditable;

class DakoiiUserModel extends Model
{
    use Auditable;

    protected $beforeInsert = ['createAudit'];
    protected $beforeUpdate = ['updateAudit'];
    protected $beforeDelete = ['deleteAudit'];

    protected function createAudit(array $data) { return $this->audit('create', $data); }
    protected function updateAudit(array $data) { return $this->audit('update', $data); }
    protected function deleteAudit(array $data) { return $this->audit('delete', $data); }

    // ... rest of model
}
```

### Log SELECT Queries (Optional)

If you need to log read operations, add this to `app/Config/Events.php`:

```php
Events::on('DBQuery', static function($query) {
    if (stripos($query->getQuery(), 'select') === 0) {
        // Log SELECT queries here
        $auditModel = new \App\Models\DakoiiAuditLogModel();
        $session = session();
        $request = \Config\Services::request();

        $auditModel->insert([
            'table_name'  => 'system',
            'primary_key' => 'query',
            'action'      => 'read',
            'old_data'    => null,
            'new_data'    => json_encode(['query' => $query->getQuery()]),
            'user_id'     => $session->get('dakoii_user_id'),
            'username'    => $session->get('dakoii_username') ?? 'system',
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => $request->getUserAgent()->getAgentString(),
        ]);
    }
});
```

## Implementation Benefits

This comprehensive audit trail implementation provides:

1. **Zero boilerplate per model** – just extend `BaseModel`
2. **No custom DB drivers** – pure CI4 model callbacks
3. **Single table** keeps reporting simple (you can still shard by month/year later)
4. **Trait flag** lets you disable auditing case-by-case (bulk imports, tests, etc.)
5. **Automatic CRUD tracking** for all database operations
6. **Manual logging capability** for authentication and system events
7. **Detailed change tracking** with before/after values
8. **User-friendly interface** to browse, filter, and search audit logs
9. **Proper security** to ensure only authorized users can access audit information
10. **Battle-tested pattern** that's been proven in production environments

## Implementation Checklist

- [ ] Create `dakoii_audit_logs` table using migration
- [ ] Create `Auditable` trait (`app/Traits/Auditable.php`)
- [ ] Create `BaseModel` class (`app/Models/BaseModel.php`)
- [ ] Create `DakoiiAuditLogModel` (`app/Models/DakoiiAuditLogModel.php`)
- [ ] Create `DakoiiAuditService` (optional, `app/Libraries/DakoiiAuditService.php`)
- [ ] Register audit service in `Services.php` (if using service)
- [ ] Update existing models to extend `BaseModel`
- [ ] Add manual audit logging to controllers (authentication events)
- [ ] Create `DakoiiAuditController` (`app/Controllers/DakoiiAuditController.php`)
- [ ] Create audit views (`dakoii_audit_logs.php`, `dakoii_audit_log_detail.php`)
- [ ] Add audit routes to `Routes.php`
- [ ] Add audit link to navigation menu
- [ ] Test the implementation with CRUD operations
- [ ] Verify audit logs are being created correctly

## Why This Approach?

This implementation combines the best of both worlds:

- **Automatic tracking** through model callbacks ensures nothing is missed
- **Simplified schema** with a single table makes reporting and maintenance easier
- **Battle-tested pattern** that has been proven in production environments
- **CodeIgniter 4 native** approach using built-in features
- **Flexible configuration** allows selective auditing and manual logging
- **Performance optimized** with minimal overhead on normal operations

This approach follows the existing architectural patterns of the Dakoii portal while adding a robust, production-ready audit trail capability that will enhance security and accountability throughout the system.