# Dakoii Portal Audit Trail Implementation Guide

This document outlines the implementation of an audit trail feature for the Dakoii portal system. The audit trail will track user actions within the portal, which is important for security, accountability, and troubleshooting.

## 1. Database Setup

Create an audit log table to store all activity data:

```sql
CREATE TABLE audit_logs (
    id                BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id           BIGINT UNSIGNED NULL,
    username          VARCHAR(50) NULL,
    ip_address        VARCHAR(45) NOT NULL,
    event_type        VARCHAR(50) NOT NULL,
    module            VARCHAR(50) NOT NULL,
    entity_type       VARCHAR(50) NOT NULL,
    entity_id         BIGINT UNSIGNED NULL,
    action            VARCHAR(50) NOT NULL,
    old_values        TEXT NULL,
    new_values        TEXT NULL,
    description       TEXT NOT NULL,
    user_agent        TEXT NULL,
    created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    /* Indexes */
    INDEX idx_user       (user_id),
    INDEX idx_username   (username),
    INDEX idx_event_type (event_type),
    INDEX idx_module     (module),
    INDEX idx_entity     (entity_type, entity_id),
    INDEX idx_action     (action),
    INDEX idx_created_at (created_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci;
```

## 2. Create an Audit Model

Create a new model file at `app/Models/AuditLogModel.php`:

```php
<?php

namespace App\Models;

use CodeIgniter\Model;

class AuditLogModel extends Model
{
    protected $table = 'audit_logs';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'user_id', 'username', 'ip_address', 'event_type', 
        'module', 'entity_type', 'entity_id', 'action', 
        'old_values', 'new_values', 'description', 'user_agent'
    ];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = '';
    protected $deletedField = '';

    /**
     * Log an action in the audit trail
     *
     * @param string $eventType   Type of event (e.g., 'authentication', 'data_change')
     * @param string $module      System module (e.g., 'users', 'organizations')
     * @param string $entityType  Type of entity affected (e.g., 'dakoii_user', 'organization')
     * @param int|null $entityId  ID of the entity affected
     * @param string $action      Action performed (e.g., 'create', 'update', 'delete', 'login')
     * @param array|null $oldValues  Previous values (for updates)
     * @param array|null $newValues  New values (for creates/updates)
     * @param string $description Human-readable description of the action
     * @return bool
     */
    public function logActivity($eventType, $module, $entityType, $entityId, $action, $oldValues = null, $newValues = null, $description)
    {
        $session = session();
        $request = \Config\Services::request();
        
        $data = [
            'user_id' => $session->get('dakoii_user_id') ?? null,
            'username' => $session->get('dakoii_username') ?? 'system',
            'ip_address' => $request->getIPAddress(),
            'event_type' => $eventType,
            'module' => $module,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'action' => $action,
            'old_values' => is_array($oldValues) ? json_encode($oldValues) : $oldValues,
            'new_values' => is_array($newValues) ? json_encode($newValues) : $newValues,
            'description' => $description,
            'user_agent' => $request->getUserAgent()->getAgentString()
        ];
        
        return $this->insert($data);
    }
}
```

## 3. Create an Audit Service

Create a service file at `app/Libraries/AuditService.php`:

```php
<?php

namespace App\Libraries;

use App\Models\AuditLogModel;

class AuditService
{
    protected $auditLogModel;
    
    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }
    
    /**
     * Log user authentication events
     */
    public function logAuthentication($action, $description, $userId = null)
    {
        return $this->auditLogModel->logActivity(
            'authentication',
            'auth',
            'dakoii_user',
            $userId,
            $action,
            null,
            null,
            $description
        );
    }
    
    /**
     * Log data changes
     */
    public function logDataChange($module, $entityType, $entityId, $action, $oldValues, $newValues, $description)
    {
        return $this->auditLogModel->logActivity(
            'data_change',
            $module,
            $entityType,
            $entityId,
            $action,
            $oldValues,
            $newValues,
            $description
        );
    }
    
    /**
     * Log system events
     */
    public function logSystemEvent($module, $action, $description)
    {
        return $this->auditLogModel->logActivity(
            'system',
            $module,
            'system',
            null,
            $action,
            null,
            null,
            $description
        );
    }
}
```

## 4. Register the Service

Update your `app/Config/Services.php` file to include the audit service:

```php
public static function auditService(bool $getShared = true)
{
    if ($getShared) {
        return static::getSharedInstance('auditService');
    }
    
    return new \App\Libraries\AuditService();
}
```

## 5. Implement a Base Model with Audit Capabilities

Create a new file at `app/Models/AuditableModel.php`:

```php
<?php

namespace App\Models;

use CodeIgniter\Model;

class AuditableModel extends Model
{
    protected $auditEnabled = true;
    protected $auditModule = '';
    protected $auditEntityType = '';
    
    /**
     * Override the insert method to add audit
     */
    public function insert($data = null, bool $returnID = true)
    {
        $result = parent::insert($data, $returnID);
        
        if ($result && $this->auditEnabled) {
            $id = $returnID ? $result : $this->getInsertID();
            $this->logAudit('create', null, $data, $id);
        }
        
        return $result;
    }
    
    /**
     * Override the update method to add audit
     */
    public function update($id = null, $data = null): bool
    {
        $oldData = null;
        
        if ($this->auditEnabled && $id) {
            // For single record updates
            if (is_numeric($id) || is_string($id)) {
                $oldData = $this->find($id);
            }
        }
        
        $result = parent::update($id, $data);
        
        if ($result && $this->auditEnabled) {
            $this->logAudit('update', $oldData, $data, $id);
        }
        
        return $result;
    }
    
    /**
     * Override the delete method to add audit
     */
    public function delete($id = null, bool $purge = false)
    {
        $oldData = null;
        
        if ($this->auditEnabled && $id) {
            // For single record deletes
            if (is_numeric($id) || is_string($id)) {
                $oldData = $this->find($id);
            }
        }
        
        $result = parent::delete($id, $purge);
        
        if ($result && $this->auditEnabled) {
            $this->logAudit('delete', $oldData, null, $id);
        }
        
        return $result;
    }
    
    /**
     * Log an audit record
     */
    protected function logAudit($action, $oldData, $newData, $id)
    {
        $auditService = \Config\Services::auditService();
        
        $description = "Record {$action} in {$this->table}";
        if (is_numeric($id) || is_string($id)) {
            $description .= " with ID: {$id}";
        }
        
        $auditService->logDataChange(
            $this->auditModule ?: $this->table,
            $this->auditEntityType ?: $this->table,
            $id,
            $action,
            $oldData,
            $newData,
            $description
        );
    }
}
```

## 6. Update Existing Models to Use the Auditable Model

Modify your existing models to extend the AuditableModel instead of the standard Model. For example:

```php
<?php

namespace App\Models;

class DakoiiUserModel extends AuditableModel
{
    protected $auditModule = 'users';
    protected $auditEntityType = 'dakoii_user';
    
    // ... rest of the existing model code
}
```

Do the same for other models like:
- OrganizationModel
- CountryModel
- ProvinceModel
- DistrictModel
- LlgModel
- OrganizationImageModel

## 7. Add Audit Logging to Controllers

Update the controllers to use the audit service. For example, in `DakoiiAuthController.php`:

```php
// In authenticateUser method, after successful login:
$auditService = \Config\Services::auditService();
$auditService->logAuthentication(
    'login',
    "User {$user['username']} logged in successfully",
    $user['id']
);

// In logoutUser method:
$auditService = \Config\Services::auditService();
if ($userId) {
    $auditService->logAuthentication(
        'logout',
        "User {$username} logged out",
        $userId
    );
}
```

## 8. Create an Audit Trail Interface

Create a controller to view the audit logs:

```php
<?php

namespace App\Controllers;

use App\Models\AuditLogModel;

class DakoiiAuditController extends BaseController
{
    protected $auditLogModel;
    
    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }
    
    public function index()
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('dakoii_role'), ['admin', 'moderator'])) {
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }
        
        $page = $this->request->getVar('page') ? (int)$this->request->getVar('page') : 1;
        $module = $this->request->getVar('module');
        $user = $this->request->getVar('user');
        $action = $this->request->getVar('action');
        $dateFrom = $this->request->getVar('date_from');
        $dateTo = $this->request->getVar('date_to');
        
        // Build query with filters
        $query = $this->auditLogModel;
        
        if ($module) {
            $query = $query->where('module', $module);
        }
        
        if ($user) {
            $query = $query->like('username', $user);
        }
        
        if ($action) {
            $query = $query->where('action', $action);
        }
        
        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }
        
        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }
        
        // Get paginated results
        $logs = $query->orderBy('created_at', 'DESC')
                      ->paginate(20, 'audit');
        
        $data = [
            'title' => 'Audit Trail',
            'logs' => $logs,
            'pager' => $this->auditLogModel->pager,
            'modules' => $this->getUniqueModules(),
            'actions' => $this->getUniqueActions(),
            'filters' => [
                'module' => $module,
                'user' => $user,
                'action' => $action,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ];
        
        return view('dakoii/dakoii_audit_logs', $data);
    }
    
    private function getUniqueModules()
    {
        return $this->auditLogModel->select('module')
                                  ->distinct()
                                  ->orderBy('module', 'ASC')
                                  ->findAll();
    }
    
    private function getUniqueActions()
    {
        return $this->auditLogModel->select('action')
                                  ->distinct()
                                  ->orderBy('action', 'ASC')
                                  ->findAll();
    }
    
    public function view($id)
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('dakoii_role'), ['admin', 'moderator'])) {
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }
        
        $log = $this->auditLogModel->find($id);
        
        if (!$log) {
            return redirect()->to(base_url('dakoii/audit'))
                ->with('error', 'Audit log entry not found.');
        }
        
        $data = [
            'title' => 'Audit Log Details',
            'log' => $log
        ];
        
        return view('dakoii/dakoii_audit_log_detail', $data);
    }
}
```

## 9. Create Audit Log Views

Create a view file at `app/Views/dakoii/dakoii_audit_logs.php`:

```php
<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Audit Trail</h3>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Module</label>
                                    <select name="module" class="form-control">
                                        <option value="">All Modules</option>
                                        <?php foreach ($modules as $mod): ?>
                                        <option value="<?= $mod['module'] ?>" <?= ($filters['module'] == $mod['module']) ? 'selected' : '' ?>>
                                            <?= ucfirst($mod['module']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Action</label>
                                    <select name="action" class="form-control">
                                        <option value="">All Actions</option>
                                        <?php foreach ($actions as $act): ?>
                                        <option value="<?= $act['action'] ?>" <?= ($filters['action'] == $act['action']) ? 'selected' : '' ?>>
                                            <?= ucfirst($act['action']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Username</label>
                                    <input type="text" name="user" class="form-control" value="<?= $filters['user'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Date From</label>
                                    <input type="date" name="date_from" class="form-control" value="<?= $filters['date_from'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Date To</label>
                                    <input type="date" name="date_to" class="form-control" value="<?= $filters['date_to'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">Filter</button>
                                        <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-secondary">Reset</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- Audit Logs Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>User</th>
                                    <th>Module</th>
                                    <th>Action</th>
                                    <th>Entity</th>
                                    <th>Description</th>
                                    <th>IP Address</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($logs)): ?>
                                <tr>
                                    <td colspan="8" class="text-center">No audit logs found</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></td>
                                        <td><?= esc($log['username']) ?></td>
                                        <td><?= ucfirst(esc($log['module'])) ?></td>
                                        <td><?= ucfirst(esc($log['action'])) ?></td>
                                        <td>
                                            <?= ucfirst(esc($log['entity_type'])) ?>
                                            <?= $log['entity_id'] ? "#{$log['entity_id']}" : '' ?>
                                        </td>
                                        <td><?= esc($log['description']) ?></td>
                                        <td><?= esc($log['ip_address']) ?></td>
                                        <td>
                                            <a href="<?= base_url('dakoii/audit/view/' . $log['id']) ?>" class="btn btn-sm btn-info">
                                                Details
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="mt-3">
                        <?= $pager->links('audit', 'bootstrap_pagination') ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

Create a view for detailed audit log entries at `app/Views/dakoii/dakoii_audit_log_detail.php`:

```php
<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Audit Log Details</h3>
                    <div class="card-tools">
                        <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>General Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">Timestamp</th>
                                    <td><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <th>User</th>
                                    <td><?= esc($log['username']) ?> (ID: <?= $log['user_id'] ?: 'N/A' ?>)</td>
                                </tr>
                                <tr>
                                    <th>IP Address</th>
                                    <td><?= esc($log['ip_address']) ?></td>
                                </tr>
                                <tr>
                                    <th>Event Type</th>
                                    <td><?= ucfirst(esc($log['event_type'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Module</th>
                                    <td><?= ucfirst(esc($log['module'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Action</th>
                                    <td><?= ucfirst(esc($log['action'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Entity</th>
                                    <td>
                                        <?= ucfirst(esc($log['entity_type'])) ?>
                                        <?= $log['entity_id'] ? "#{$log['entity_id']}" : 'N/A' ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Description</th>
                                    <td><?= esc($log['description']) ?></td>
                                </tr>
                                <tr>
                                    <th>User Agent</th>
                                    <td><small><?= esc($log['user_agent']) ?></small></td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <?php if ($log['old_values'] || $log['new_values']): ?>
                                <h5>Data Changes</h5>
                                
                                <?php if ($log['old_values']): ?>
                                    <h6 class="mt-3">Old Values</h6>
                                    <div class="p-3 bg-light pre-wrap">
                                        <pre><?= json_encode(json_decode($log['old_values']), JSON_PRETTY_PRINT) ?></pre>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($log['new_values']): ?>
                                    <h6 class="mt-3">New Values</h6>
                                    <div class="p-3 bg-light pre-wrap">
                                        <pre><?= json_encode(json_decode($log['new_values']), JSON_PRETTY_PRINT) ?></pre>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

## 10. Update Routes

Add the audit routes in `app/Config/Routes.php`:

```php
// Audit Trail routes
$routes->group('dakoii', ['filter' => 'dakoiiAuth'], function ($routes) {
    $routes->get('audit', 'DakoiiAuditController::index');
    $routes->get('audit/view/(:num)', 'DakoiiAuditController::view/$1');
});
```

## 11. Add to Navigation

Update your portal navigation menu to include the audit trail link:

```php
<!-- In your dakoii_portal_template.php file -->
<li class="nav-item">
    <a href="<?= base_url('dakoii/audit') ?>" class="nav-link <?= (strpos(current_url(), 'audit') !== false) ? 'active' : '' ?>">
        <i class="nav-icon fas fa-history"></i>
        <p>Audit Trail</p>
    </a>
</li>
```

## Implementation Benefits

This comprehensive audit trail implementation will:

1. Track all CRUD operations automatically through the AuditableModel
2. Log authentication events (login, logout, password resets)
3. Store detailed information including:
   - Who performed the action (user)
   - What was done (action)
   - When it occurred (timestamp)
   - Where it was done (IP address, user agent)
   - Which part of the system was affected (module, entity)
   - What data changed (old and new values)

4. Provide a user-friendly interface to browse, filter, and search audit logs
5. Implement proper security to ensure only authorized users can access audit information

## Implementation Checklist

- [ ] Create audit_logs table in the database
- [ ] Create AuditLogModel.php
- [ ] Create AuditService.php
- [ ] Register audit service in Services.php
- [ ] Create AuditableModel.php
- [ ] Update existing models to extend AuditableModel
- [ ] Add audit logging to controllers
- [ ] Create DakoiiAuditController.php
- [ ] Create audit views (dakoii_audit_logs.php, dakoii_audit_log_detail.php)
- [ ] Add audit routes to Routes.php
- [ ] Add audit link to navigation menu

This approach follows the existing architectural patterns of the Dakoii portal while adding a robust audit trail capability that will enhance security and accountability throughout the system.