# Dakoii Portal Audit Trail Implementation Guide

This document outlines the implementation of a comprehensive audit trail feature **specifically for the Dakoii portal system**. This audit trail will track user actions within the Dakoii portal only, which is important for security, accountability, and troubleshooting of Dakoii-related activities.

**Important Note**: This implementation is portal-specific. Separate audit trail systems will be implemented for:
- Admin Portal (with its own `admin_audit_logs` table)
- Project Monitoring Portal (with its own `project_audit_logs` table)

This implementation uses a battle-tested pattern that captures **only CRUD activity** (create, read*, update, delete) in a single audit table specific to the Dakoii portal.

*Note: We normally skip plain "reads" because they don't alter state, but you can easily add them if needed.*

## 1. Database Setup - Dakoii Portal Specific

### Option 1: Using CodeIgniter 4 Migration (Recommended)

Create the migration file:

```bash
php spark make:migration CreateDakoiiAuditLogsTable
```

Update the migration file:

```php
// database/migrations/YYYY-MM-DD-HHMMSS_CreateDakoiiAuditLogsTable.php
<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateDakoiiAuditLogsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id'          => ['type' => 'BIGINT', 'auto_increment' => true],
            'table_name'  => ['type' => 'VARCHAR', 'constraint' => 100, 'comment' => 'Name of the table that was modified'],
            'primary_key' => ['type' => 'VARCHAR', 'constraint' => 64, 'comment' => 'Primary key value of the affected record'],
            'action'      => ['type' => 'ENUM', 'constraint' => ['create','update','delete'], 'comment' => 'Type of CRUD operation'],
            'old_data'    => ['type' => 'MEDIUMTEXT', 'null' => true, 'comment' => 'JSON of data before change (for updates/deletes)'],
            'new_data'    => ['type' => 'MEDIUMTEXT', 'null' => true, 'comment' => 'JSON of data after change (for creates/updates)'],
            'dakoii_user_id' => ['type' => 'BIGINT', 'null' => true, 'comment' => 'ID of the Dakoii user who performed the action'],
            'dakoii_username' => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true, 'comment' => 'Username of the Dakoii user'],
            'ip_address'  => ['type' => 'VARCHAR', 'constraint' => 45, 'null' => true, 'comment' => 'IP address of the user'],
            'user_agent'  => ['type' => 'TEXT', 'null' => true, 'comment' => 'Browser/client user agent string'],
            'session_id'  => ['type' => 'VARCHAR', 'constraint' => 128, 'null' => true, 'comment' => 'Dakoii portal session ID'],
            'portal_context' => ['type' => 'VARCHAR', 'constraint' => 20, 'default' => 'dakoii', 'comment' => 'Always "dakoii" for this portal'],
            'created_at'  => ['type' => 'DATETIME', 'default' => 'CURRENT_TIMESTAMP', 'comment' => 'When the audit log was created'],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('table_name');
        $this->forge->addKey('dakoii_user_id');
        $this->forge->addKey('action');
        $this->forge->addKey('created_at');
        $this->forge->addKey('portal_context');

        $this->forge->createTable('dakoii_audit_logs');
    }

    public function down()
    {
        $this->forge->dropTable('dakoii_audit_logs');
    }
}
```

Run the migration:

```bash
php spark migrate
```

### Option 2: Direct SQL (Alternative)

```sql
CREATE TABLE dakoii_audit_logs (
    id                BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    table_name        VARCHAR(100) NOT NULL COMMENT 'Name of the table that was modified',
    primary_key       VARCHAR(64) NOT NULL COMMENT 'Primary key value of the affected record',
    action            ENUM('create','update','delete') NOT NULL COMMENT 'Type of CRUD operation',
    old_data          MEDIUMTEXT NULL COMMENT 'JSON of data before change (for updates/deletes)',
    new_data          MEDIUMTEXT NULL COMMENT 'JSON of data after change (for creates/updates)',
    dakoii_user_id    BIGINT UNSIGNED NULL COMMENT 'ID of the Dakoii user who performed the action',
    dakoii_username   VARCHAR(50) NULL COMMENT 'Username of the Dakoii user',
    ip_address        VARCHAR(45) NULL COMMENT 'IP address of the user',
    user_agent        TEXT NULL COMMENT 'Browser/client user agent string',
    session_id        VARCHAR(128) NULL COMMENT 'Dakoii portal session ID',
    portal_context    VARCHAR(20) DEFAULT 'dakoii' COMMENT 'Always "dakoii" for this portal',
    created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When the audit log was created',

    /* Indexes for efficient querying */
    INDEX idx_table_name (table_name),
    INDEX idx_dakoii_user_id (dakoii_user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_portal_context (portal_context),
    INDEX idx_session_id (session_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE      = utf8mb4_unicode_ci
  COMMENT = 'Audit trail for Dakoii Portal activities only';
```

## 2. Create the Dakoii-Specific Auditable Trait

Create a reusable trait specifically for Dakoii portal at `app/Traits/DakoiiAuditable.php`:

```php
<?php

namespace App\Traits;

use Config\Services;
use App\Models\DakoiiAuditLogModel;

/**
 * Dakoii Portal Specific Auditable Trait
 *
 * This trait is specifically designed for the Dakoii portal and should only be used
 * by models that are part of the Dakoii portal functionality.
 *
 * For other portals (Admin, Project Monitoring), separate audit traits will be created.
 */
trait DakoiiAuditable
{
    protected bool $dakoiiAuditSkip = false;   // allows manual opt-out per call

    // Automatically attached in Model::$beforeInsert / $beforeUpdate / $beforeDelete
    protected function dakoiiAudit(string $action, array $data): array
    {
        if ($this->dakoiiAuditSkip) {
            return $data;
        }   // skip when needed

        // Only audit if we're in a Dakoii portal context
        if (!$this->isDakoiiPortalContext()) {
            return $data;
        }

        $request   = Services::request();
        $session   = session();
        $userId    = $session->get('dakoii_user_id') ?? null;
        $username  = $session->get('dakoii_username') ?? 'system';
        $ip        = $request->getIPAddress();
        $userAgent = $request->getUserAgent()->getAgentString();
        $sessionId = $session->session_id ?? null;
        $pkField   = $this->primaryKey;
        $pk        = $data['id'] ?? $data['data'][$pkField] ?? null;

        $old = null;
        $new = null;

        if ($action === 'update') {
            $old = json_encode($this->asArray()
                    ->where($pkField, $pk)
                    ->first(), JSON_UNESCAPED_UNICODE);
            $new = json_encode($data['data'], JSON_UNESCAPED_UNICODE);
        } elseif ($action === 'create') {
            $new = json_encode($data['data'], JSON_UNESCAPED_UNICODE);
        } elseif ($action === 'delete') {
            $old = json_encode($this->asArray()->where($pkField, $pk)->first(), JSON_UNESCAPED_UNICODE);
        }

        (new DakoiiAuditLogModel())->insert([
            'table_name'      => $this->table,
            'primary_key'     => (string) $pk,
            'action'          => $action,
            'old_data'        => $old,
            'new_data'        => $new,
            'dakoii_user_id'  => $userId,
            'dakoii_username' => $username,
            'ip_address'      => $ip,
            'user_agent'      => $userAgent,
            'session_id'      => $sessionId,
            'portal_context'  => 'dakoii',
        ]);

        return $data; // MUST return to keep the normal pipeline flowing
    }

    /**
     * Check if we're currently in a Dakoii portal context
     * This helps ensure we only audit Dakoii-related activities
     */
    protected function isDakoiiPortalContext(): bool
    {
        $session = session();

        // Check if user is logged into Dakoii portal
        if ($session->get('dakoii_user_id')) {
            return true;
        }

        // Check URL pattern for Dakoii routes
        $request = Services::request();
        $uri = $request->getUri()->getPath();
        if (strpos($uri, '/dakoii/') !== false || strpos($uri, 'dakoii') !== false) {
            return true;
        }

        return false;
    }
}
```

## 3. Create the Dakoii Base Model

Create a base model specifically for Dakoii portal models at `app/Models/DakoiiBaseModel.php`:

```php
<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Traits\DakoiiAuditable;

/**
 * Base Model for Dakoii Portal
 *
 * This base model should only be extended by models that are part of the Dakoii portal.
 * It provides automatic audit logging for all CRUD operations within the Dakoii context.
 *
 * For other portals:
 * - Admin Portal models should extend AdminBaseModel (to be created)
 * - Project Monitoring models should extend ProjectBaseModel (to be created)
 */
class DakoiiBaseModel extends Model
{
    use DakoiiAuditable;

    protected $beforeInsert = ['createDakoiiAudit'];
    protected $beforeUpdate = ['updateDakoiiAudit'];
    protected $beforeDelete = ['deleteDakoiiAudit'];

    protected function createDakoiiAudit(array $data)
    {
        return $this->dakoiiAudit('create', $data);
    }

    protected function updateDakoiiAudit(array $data)
    {
        return $this->dakoiiAudit('update', $data);
    }

    protected function deleteDakoiiAudit(array $data)
    {
        return $this->dakoiiAudit('delete', $data);
    }

    /**
     * Skip Dakoii audit logging for the next operation
     * Useful for bulk operations, seeders, or system operations
     */
    public function skipDakoiiAudit(): self
    {
        $this->dakoiiAuditSkip = true;
        return $this;
    }
}
```

## 4. Create the Dakoii Audit Log Model

Create the audit log model specifically for Dakoii portal at `app/Models/DakoiiAuditLogModel.php`:

```php
<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Dakoii Portal Audit Log Model
 *
 * This model handles audit logs specifically for the Dakoii portal.
 * It should NOT be used for other portals (Admin, Project Monitoring).
 */
class DakoiiAuditLogModel extends Model
{
    protected $table      = 'dakoii_audit_logs';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'table_name', 'primary_key', 'action',
        'old_data', 'new_data', 'dakoii_user_id', 'dakoii_username',
        'ip_address', 'user_agent', 'session_id', 'portal_context', 'created_at',
    ];

    public $timestamps   = false; // we already store created_at manually

    /**
     * Get audit logs for a specific table (Dakoii portal only)
     */
    public function getDakoiiTableLogs(string $tableName, int $limit = 20): array
    {
        return $this->where('table_name', $tableName)
                   ->where('portal_context', 'dakoii')
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get audit logs for a specific record (Dakoii portal only)
     */
    public function getDakoiiRecordLogs(string $tableName, string $primaryKey, int $limit = 20): array
    {
        return $this->where('table_name', $tableName)
                   ->where('primary_key', $primaryKey)
                   ->where('portal_context', 'dakoii')
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get audit logs by Dakoii user
     */
    public function getDakoiiUserLogs(int $dakoiiUserId, int $limit = 20): array
    {
        return $this->where('dakoii_user_id', $dakoiiUserId)
                   ->where('portal_context', 'dakoii')
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get audit logs by Dakoii session
     */
    public function getDakoiiSessionLogs(string $sessionId, int $limit = 20): array
    {
        return $this->where('session_id', $sessionId)
                   ->where('portal_context', 'dakoii')
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get recent Dakoii portal activity
     */
    public function getRecentDakoiiActivity(int $limit = 50): array
    {
        return $this->where('portal_context', 'dakoii')
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get Dakoii audit statistics
     */
    public function getDakoiiAuditStats(): array
    {
        $stats = [];

        // Total Dakoii audit logs
        $stats['total_logs'] = $this->where('portal_context', 'dakoii')->countAllResults();

        // Logs by action
        $stats['by_action'] = $this->select('action, COUNT(*) as count')
                                  ->where('portal_context', 'dakoii')
                                  ->groupBy('action')
                                  ->findAll();

        // Most active Dakoii users
        $stats['active_users'] = $this->select('dakoii_username, COUNT(*) as count')
                                      ->where('portal_context', 'dakoii')
                                      ->where('dakoii_username IS NOT NULL')
                                      ->groupBy('dakoii_username')
                                      ->orderBy('count', 'DESC')
                                      ->findAll(10);

        // Most modified tables
        $stats['active_tables'] = $this->select('table_name, COUNT(*) as count')
                                       ->where('portal_context', 'dakoii')
                                       ->groupBy('table_name')
                                       ->orderBy('count', 'DESC')
                                       ->findAll(10);

        return $stats;
    }
}
```

## 5. Update Dakoii-Related Models to Use DakoiiBaseModel

Now every Dakoii domain model that **extends DakoiiBaseModel** gains audit logging automatically. Update your existing Dakoii-related models:

```php
<?php

namespace App\Models;

/**
 * Dakoii User Model - Specific to Dakoii Portal
 * Extends DakoiiBaseModel to get automatic audit logging for Dakoii activities
 */
class DakoiiUserModel extends DakoiiBaseModel
{
    protected $table      = 'dakoii_users';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'username', 'email', 'password', 'role', 'status',
        'first_name', 'last_name', 'phone', 'organization_id'
    ];
    // ... rest of existing model code
}
```

```php
<?php

namespace App\Models;

/**
 * Organization Model - Used by Dakoii Portal
 * Extends DakoiiBaseModel to get automatic audit logging when modified via Dakoii portal
 */
class OrganizationModel extends DakoiiBaseModel
{
    protected $table      = 'dakoii_organizations';  // Note: using dakoii_ prefix
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'name', 'type', 'description', 'status',
        'country_id', 'province_id', 'district_id', 'llg_id'
    ];
    // ... rest of existing model code
}
```

**Important**: Only update models that are specifically used by the Dakoii portal:

**Dakoii-Specific Models** (should extend DakoiiBaseModel):
- DakoiiUserModel
- OrganizationModel (when accessed via Dakoii portal)
- OrganizationImageModel (when accessed via Dakoii portal)

**Shared/Reference Models** (should NOT extend DakoiiBaseModel):
- CountryModel (shared across all portals)
- ProvinceModel (shared across all portals)
- DistrictModel (shared across all portals)
- LlgModel (shared across all portals)

**Note**: Shared models like Country, Province, District, and LLG should remain as regular CodeIgniter models since they are used across multiple portals and should not be tied to any specific portal's audit system.

## 6. Create a Dakoii Audit Service (Optional - for Manual Logging)

For cases where you need to log events that aren't captured by model callbacks (like authentication events), create a service specifically for Dakoii portal at `app/Libraries/DakoiiAuditService.php`:

```php
<?php

namespace App\Libraries;

use App\Models\DakoiiAuditLogModel;
use Config\Services;

/**
 * Dakoii Portal Audit Service
 *
 * This service handles manual audit logging specifically for the Dakoii portal.
 * It should only be used for Dakoii portal activities.
 *
 * For other portals, separate audit services will be created:
 * - AdminAuditService for Admin Portal
 * - ProjectAuditService for Project Monitoring Portal
 */
class DakoiiAuditService
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new DakoiiAuditLogModel();
    }

    /**
     * Log Dakoii authentication events manually
     */
    public function logDakoiiAuthentication(string $action, string $description, int $dakoiiUserId = null): bool
    {
        $request = Services::request();
        $session = session();

        $data = [
            'table_name'      => 'dakoii_users',
            'primary_key'     => (string) ($dakoiiUserId ?? $session->get('dakoii_user_id') ?? 'unknown'),
            'action'          => $action, // Note: this extends beyond create/update/delete for auth events
            'old_data'        => null,
            'new_data'        => json_encode(['description' => $description, 'event_type' => 'authentication'], JSON_UNESCAPED_UNICODE),
            'dakoii_user_id'  => $dakoiiUserId ?? $session->get('dakoii_user_id'),
            'dakoii_username' => $session->get('dakoii_username') ?? 'system',
            'ip_address'      => $request->getIPAddress(),
            'user_agent'      => $request->getUserAgent()->getAgentString(),
            'session_id'      => $session->session_id ?? null,
            'portal_context'  => 'dakoii',
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log Dakoii system events manually
     */
    public function logDakoiiSystemEvent(string $tableName, string $action, string $description): bool
    {
        $request = Services::request();
        $session = session();

        $data = [
            'table_name'      => $tableName,
            'primary_key'     => 'system',
            'action'          => $action,
            'old_data'        => null,
            'new_data'        => json_encode(['description' => $description, 'event_type' => 'system'], JSON_UNESCAPED_UNICODE),
            'dakoii_user_id'  => $session->get('dakoii_user_id'),
            'dakoii_username' => $session->get('dakoii_username') ?? 'system',
            'ip_address'      => $request->getIPAddress(),
            'user_agent'      => $request->getUserAgent()->getAgentString(),
            'session_id'      => $session->session_id ?? null,
            'portal_context'  => 'dakoii',
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log Dakoii portal access events
     */
    public function logDakoiiPortalAccess(string $page, string $action = 'view'): bool
    {
        $request = Services::request();
        $session = session();

        $data = [
            'table_name'      => 'dakoii_portal_access',
            'primary_key'     => $page,
            'action'          => $action,
            'old_data'        => null,
            'new_data'        => json_encode([
                'page' => $page,
                'url' => current_url(),
                'event_type' => 'portal_access'
            ], JSON_UNESCAPED_UNICODE),
            'dakoii_user_id'  => $session->get('dakoii_user_id'),
            'dakoii_username' => $session->get('dakoii_username') ?? 'anonymous',
            'ip_address'      => $request->getIPAddress(),
            'user_agent'      => $request->getUserAgent()->getAgentString(),
            'session_id'      => $session->session_id ?? null,
            'portal_context'  => 'dakoii',
        ];

        return $this->auditLogModel->insert($data);
    }
}
```

## 7. Register the Dakoii Audit Service (Optional)

Update your `app/Config/Services.php` file to include the Dakoii audit service:

```php
/**
 * Dakoii Portal Audit Service
 * Handles audit logging specifically for Dakoii portal activities
 */
public static function dakoiiAuditService(bool $getShared = true)
{
    if ($getShared) {
        return static::getSharedInstance('dakoiiAuditService');
    }

    return new \App\Libraries\DakoiiAuditService();
}
```

## 8. Add Manual Audit Logging to Dakoii Controllers

For authentication events and other actions not captured by model callbacks, update your Dakoii controllers. For example, in `DakoiiAuthController.php`:

```php
// In authenticateUser method, after successful login:
$auditService = \Config\Services::dakoiiAuditService();
$auditService->logDakoiiAuthentication(
    'login',
    "Dakoii user {$user['username']} logged in successfully",
    $user['id']
);

// In logoutUser method:
$auditService = \Config\Services::dakoiiAuditService();
if ($userId) {
    $auditService->logDakoiiAuthentication(
        'logout',
        "Dakoii user {$username} logged out",
        $userId
    );
}

// For password reset events:
$auditService->logDakoiiAuthentication(
    'password_reset',
    "Password reset requested for Dakoii user {$username}",
    $userId
);

// For failed login attempts:
$auditService->logDakoiiAuthentication(
    'login_failed',
    "Failed login attempt for username: {$username}",
    null
);

// For portal access logging (optional):
$auditService->logDakoiiPortalAccess('dashboard');
$auditService->logDakoiiPortalAccess('organizations');
```

## 9. Create an Audit Trail Interface

Create a controller to view the audit logs at `app/Controllers/DakoiiAuditController.php`:

```php
<?php

namespace App\Controllers;

use App\Models\DakoiiAuditLogModel;

class DakoiiAuditController extends BaseController
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new DakoiiAuditLogModel();
    }

    public function index()
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('dakoii_role'), ['admin', 'moderator'])) {
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $page = $this->request->getVar('page') ? (int)$this->request->getVar('page') : 1;
        $tableName = $this->request->getVar('table_name');
        $user = $this->request->getVar('user');
        $action = $this->request->getVar('action');
        $dateFrom = $this->request->getVar('date_from');
        $dateTo = $this->request->getVar('date_to');

        // Build query with filters
        $query = $this->auditLogModel;

        if ($tableName) {
            $query = $query->where('table_name', $tableName);
        }

        if ($user) {
            $query = $query->like('dakoii_username', $user);
        }

        if ($action) {
            $query = $query->where('action', $action);
        }

        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }

        // Ensure we only get Dakoii portal logs
        $query = $query->where('portal_context', 'dakoii');

        // Get paginated results
        $logs = $query->orderBy('created_at', 'DESC')
                      ->paginate(20, 'dakoii_audit');

        $data = [
            'title' => 'Dakoii Portal Audit Trail',
            'logs' => $logs,
            'pager' => $this->auditLogModel->pager,
            'tables' => $this->getDakoiiUniqueTables(),
            'actions' => $this->getDakoiiUniqueActions(),
            'filters' => [
                'table_name' => $tableName,
                'user' => $user,
                'action' => $action,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ];

        return view('dakoii/dakoii_audit_logs', $data);
    }

    private function getDakoiiUniqueTables()
    {
        return $this->auditLogModel->select('table_name')
                                  ->where('portal_context', 'dakoii')
                                  ->distinct()
                                  ->orderBy('table_name', 'ASC')
                                  ->findAll();
    }

    private function getDakoiiUniqueActions()
    {
        return $this->auditLogModel->select('action')
                                  ->where('portal_context', 'dakoii')
                                  ->distinct()
                                  ->orderBy('action', 'ASC')
                                  ->findAll();
    }

    public function view($id)
    {
        // Ensure user has proper permissions
        if (!in_array(session()->get('dakoii_role'), ['admin', 'moderator'])) {
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('error', 'You do not have permission to access audit logs.');
        }

        $log = $this->auditLogModel->find($id);

        if (!$log) {
            return redirect()->to(base_url('dakoii/audit'))
                ->with('error', 'Audit log entry not found.');
        }

        $data = [
            'title' => 'Audit Log Details',
            'log' => $log
        ];

        return view('dakoii/dakoii_audit_log_detail', $data);
    }
}
```

## 10. Create Audit Log Views

Create a view file at `app/Views/dakoii/dakoii_audit_logs.php`:

```php
<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Audit Trail</h3>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Table</label>
                                    <select name="table_name" class="form-control">
                                        <option value="">All Tables</option>
                                        <?php foreach ($tables as $table): ?>
                                        <option value="<?= $table['table_name'] ?>" <?= ($filters['table_name'] == $table['table_name']) ? 'selected' : '' ?>>
                                            <?= ucfirst(str_replace('_', ' ', $table['table_name'])) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Action</label>
                                    <select name="action" class="form-control">
                                        <option value="">All Actions</option>
                                        <?php foreach ($actions as $act): ?>
                                        <option value="<?= $act['action'] ?>" <?= ($filters['action'] == $act['action']) ? 'selected' : '' ?>>
                                            <?= ucfirst($act['action']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Username</label>
                                    <input type="text" name="user" class="form-control" value="<?= $filters['user'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Date From</label>
                                    <input type="date" name="date_from" class="form-control" value="<?= $filters['date_from'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Date To</label>
                                    <input type="date" name="date_to" class="form-control" value="<?= $filters['date_to'] ?>">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">Filter</button>
                                        <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-secondary">Reset</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Audit Logs Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>User</th>
                                    <th>Table</th>
                                    <th>Action</th>
                                    <th>Record ID</th>
                                    <th>IP Address</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($logs)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">No audit logs found</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></td>
                                        <td><?= esc($log['dakoii_username']) ?></td>
                                        <td><?= ucfirst(str_replace('_', ' ', esc($log['table_name']))) ?></td>
                                        <td>
                                            <span class="badge badge-<?= $log['action'] === 'create' ? 'success' : ($log['action'] === 'update' ? 'warning' : 'danger') ?>">
                                                <?= ucfirst(esc($log['action'])) ?>
                                            </span>
                                        </td>
                                        <td><?= esc($log['primary_key']) ?></td>
                                        <td><?= esc($log['ip_address']) ?></td>
                                        <td>
                                            <a href="<?= base_url('dakoii/audit/view/' . $log['id']) ?>" class="btn btn-sm btn-info">
                                                Details
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-3">
                        <?= $pager->links('audit', 'bootstrap_pagination') ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

Create a view for detailed audit log entries at `app/Views/dakoii/dakoii_audit_log_detail.php`:

```php
<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Audit Log Details</h3>
                    <div class="card-tools">
                        <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>General Information</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">Timestamp</th>
                                    <td><?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Dakoii User</th>
                                    <td><?= esc($log['dakoii_username']) ?> (ID: <?= $log['dakoii_user_id'] ?: 'N/A' ?>)</td>
                                </tr>
                                <tr>
                                    <th>IP Address</th>
                                    <td><?= esc($log['ip_address']) ?></td>
                                </tr>
                                <tr>
                                    <th>Table</th>
                                    <td><?= ucfirst(str_replace('_', ' ', esc($log['table_name']))) ?></td>
                                </tr>
                                <tr>
                                    <th>Action</th>
                                    <td>
                                        <span class="badge badge-<?= $log['action'] === 'create' ? 'success' : ($log['action'] === 'update' ? 'warning' : 'danger') ?>">
                                            <?= ucfirst(esc($log['action'])) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Record ID</th>
                                    <td><?= esc($log['primary_key']) ?></td>
                                </tr>
                                <tr>
                                    <th>Session ID</th>
                                    <td><?= esc($log['session_id']) ?: 'N/A' ?></td>
                                </tr>
                                <tr>
                                    <th>Portal Context</th>
                                    <td><span class="badge badge-primary"><?= ucfirst(esc($log['portal_context'])) ?></span></td>
                                </tr>
                                <tr>
                                    <th>User Agent</th>
                                    <td><small><?= esc($log['user_agent']) ?></small></td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <?php if ($log['old_data'] || $log['new_data']): ?>
                                <h5>Data Changes</h5>

                                <?php if ($log['old_data']): ?>
                                    <h6 class="mt-3">Old Values</h6>
                                    <div class="p-3 bg-light">
                                        <pre><?= json_encode(json_decode($log['old_data']), JSON_PRETTY_PRINT) ?></pre>
                                    </div>
                                <?php endif; ?>

                                <?php if ($log['new_data']): ?>
                                    <h6 class="mt-3">New Values</h6>
                                    <div class="p-3 bg-light">
                                        <pre><?= json_encode(json_decode($log['new_data']), JSON_PRETTY_PRINT) ?></pre>
                                    </div>
                                <?php endif; ?>

                                <?php if ($log['old_data'] && $log['new_data']): ?>
                                    <h6 class="mt-3">Field Changes</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Field</th>
                                                    <th>Old Value</th>
                                                    <th>New Value</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $oldData = json_decode($log['old_data'], true);
                                                $newData = json_decode($log['new_data'], true);
                                                $allFields = array_unique(array_merge(array_keys($oldData ?: []), array_keys($newData ?: [])));
                                                foreach ($allFields as $field):
                                                    $oldValue = $oldData[$field] ?? 'N/A';
                                                    $newValue = $newData[$field] ?? 'N/A';
                                                    if ($oldValue !== $newValue):
                                                ?>
                                                <tr>
                                                    <td><strong><?= esc($field) ?></strong></td>
                                                    <td><code><?= esc($oldValue) ?></code></td>
                                                    <td><code><?= esc($newValue) ?></code></td>
                                                </tr>
                                                <?php endif; endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
```

## 11. Update Routes for Dakoii Portal

Add the Dakoii audit routes in `app/Config/Routes.php`:

```php
// Dakoii Portal Audit Trail routes
$routes->group('dakoii', ['filter' => 'dakoiiAuth'], function ($routes) {
    $routes->get('audit', 'DakoiiAuditController::index');
    $routes->get('audit/view/(:num)', 'DakoiiAuditController::view/$1');
    $routes->get('audit/stats', 'DakoiiAuditController::stats'); // Optional: audit statistics
});
```

## 12. Add to Dakoii Portal Navigation

Update your Dakoii portal navigation menu to include the audit trail link:

```php
<!-- In your dakoii_portal_template.php file -->
<li class="nav-item">
    <a href="<?= base_url('dakoii/audit') ?>" class="nav-link <?= (strpos(current_url(), 'audit') !== false) ? 'active' : '' ?>">
        <i class="nav-icon fas fa-history"></i>
        <p>Dakoii Audit Trail</p>
    </a>
</li>
```

**Note**: This audit trail link should only appear in the Dakoii portal navigation, not in other portals.

## Advanced Features and Tweaks

### Skip Dakoii Auditing for Specific Operations

```php
// Skip Dakoii auditing in seeder/factory code
$userModel = new DakoiiUserModel();
$userModel->skipDakoiiAudit()->insert($userData);

// Or for bulk operations
$userModel->skipDakoiiAudit()->insertBatch($bulkUserData);
```

### Portal-Specific Model Architecture

The recommended approach is to have separate base models for each portal:

```php
<?php

namespace App\Models;

// For Dakoii Portal models
class DakoiiUserModel extends DakoiiBaseModel
{
    // Automatically gets Dakoii audit logging
}

// For Admin Portal models (to be created)
class AdminUserModel extends AdminBaseModel
{
    // Would get Admin audit logging
}

// For Project Monitoring models (to be created)
class ProjectModel extends ProjectBaseModel
{
    // Would get Project audit logging
}

// For shared/reference models (no specific portal audit)
class CountryModel extends Model
{
    // No automatic audit logging - shared across portals
}
```

### Log Dakoii SELECT Queries (Optional)

If you need to log read operations specifically for Dakoii portal, add this to `app/Config/Events.php`:

```php
Events::on('DBQuery', static function($query) {
    if (stripos($query->getQuery(), 'select') === 0) {
        $session = session();

        // Only log if user is in Dakoii portal context
        if ($session->get('dakoii_user_id')) {
            $auditModel = new \App\Models\DakoiiAuditLogModel();
            $request = \Config\Services::request();

            $auditModel->insert([
                'table_name'      => 'system',
                'primary_key'     => 'query',
                'action'          => 'read',
                'old_data'        => null,
                'new_data'        => json_encode(['query' => $query->getQuery()]),
                'dakoii_user_id'  => $session->get('dakoii_user_id'),
                'dakoii_username' => $session->get('dakoii_username') ?? 'system',
                'ip_address'      => $request->getIPAddress(),
                'user_agent'      => $request->getUserAgent()->getAgentString(),
                'session_id'      => $session->session_id ?? null,
                'portal_context'  => 'dakoii',
            ]);
        }
    }
});
```

**Warning**: This will log ALL SELECT queries when a Dakoii user is logged in, which can generate a lot of audit data. Use with caution.

## Implementation Benefits

This comprehensive Dakoii portal audit trail implementation provides:

1. **Portal-specific auditing** – only tracks Dakoii portal activities
2. **Zero boilerplate per model** – just extend `DakoiiBaseModel`
3. **No custom DB drivers** – pure CI4 model callbacks
4. **Single table per portal** keeps reporting simple and portal-isolated
5. **Context-aware auditing** – automatically detects Dakoii portal context
6. **Trait flag** lets you disable auditing case-by-case (bulk imports, tests, etc.)
7. **Automatic CRUD tracking** for all Dakoii database operations
8. **Manual logging capability** for authentication and system events
9. **Detailed change tracking** with before/after values
10. **User-friendly interface** to browse, filter, and search Dakoii audit logs
11. **Proper security** to ensure only authorized Dakoii users can access audit information
12. **Battle-tested pattern** that's been proven in production environments
13. **Scalable architecture** – ready for separate Admin and Project portal audit systems

## Implementation Checklist

- [ ] Create `dakoii_audit_logs` table using migration (with `dakoii_` prefix)
- [ ] Create `DakoiiAuditable` trait (`app/Traits/DakoiiAuditable.php`)
- [ ] Create `DakoiiBaseModel` class (`app/Models/DakoiiBaseModel.php`)
- [ ] Create `DakoiiAuditLogModel` (`app/Models/DakoiiAuditLogModel.php`)
- [ ] Create `DakoiiAuditService` (optional, `app/Libraries/DakoiiAuditService.php`)
- [ ] Register Dakoii audit service in `Services.php` (if using service)
- [ ] Update Dakoii-specific models to extend `DakoiiBaseModel`
- [ ] Keep shared models (Country, Province, etc.) as regular models
- [ ] Add manual audit logging to Dakoii controllers (authentication events)
- [ ] Create `DakoiiAuditController` (`app/Controllers/DakoiiAuditController.php`)
- [ ] Create Dakoii audit views (`dakoii_audit_logs.php`, `dakoii_audit_log_detail.php`)
- [ ] Add Dakoii audit routes to `Routes.php`
- [ ] Add audit link to Dakoii portal navigation menu only
- [ ] Test the implementation with Dakoii CRUD operations
- [ ] Verify audit logs are being created correctly for Dakoii activities only
- [ ] Ensure no audit logs are created for non-Dakoii activities

## Why This Portal-Specific Approach?

This implementation provides the best solution for a multi-portal system:

- **Portal isolation** – each portal has its own audit trail, preventing cross-contamination
- **Automatic tracking** through model callbacks ensures nothing is missed within each portal
- **Simplified schema** with portal-specific tables makes reporting and maintenance easier
- **Battle-tested pattern** that has been proven in production environments
- **CodeIgniter 4 native** approach using built-in features
- **Flexible configuration** allows selective auditing and manual logging per portal
- **Performance optimized** with minimal overhead on normal operations
- **Scalable architecture** – easy to add audit trails for other portals later
- **Context-aware** – only audits activities within the appropriate portal context

## Future Portal Audit Implementations

When implementing audit trails for other portals, follow this same pattern:

1. **Admin Portal**: Create `admin_audit_logs` table, `AdminAuditable` trait, `AdminBaseModel`, etc.
2. **Project Monitoring Portal**: Create `project_audit_logs` table, `ProjectAuditable` trait, `ProjectBaseModel`, etc.

This approach ensures each portal maintains its own audit trail while following consistent patterns across the entire system.

This approach follows the existing architectural patterns of the Dakoii portal while adding a robust, production-ready audit trail capability that will enhance security and accountability specifically for Dakoii portal activities.